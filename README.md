# VocalAuto - 语音对话自动化测试框架

## 项目简介
VocalAuto 是一个用于语音对话系统的自动化测试框架，支持实时语音对话、TTS语音合成、捏音功能等多种测试场景。

## 环境要求
- Python 3.11+
- 依赖包安装：
```bash
pip install -r requirements.txt
```

## 项目结构
```
vocalauto/
├── data/                   # 测试用例、测试数据目录
├── reports/                # 测试报告输出目录
├── utils/                  # 工具类
│   ├── excel_writer.py     # Excel写入工具
│   ├── progress_display.py # 进度显示工具
│   └── config_manager.py   # 配置管理工具
├── workflow/               # 工作流模块
│   ├── realtime_api.py     # 实时语音API处理
│   ├── realtime_workflow.py # 实时语音工作流
│   ├── tts_workflow.py     # TTS工作流
│   ├── chat_workflow.py    # 聊天工作流
│   ├── voice_create_workflow.py # 捏音工作流
│   └── role_api.py         # 角色API处理
└── run_realtime.py         # 主执行脚本
```

## 功能特性
1. 单轮、多轮对话测试
   - 支持实时语音对话测试
   - 支持 PCM 和 OGG 格式的音频文件输入
   - 支持历史对话上下文管理
   - 支持自定义最大历史轮数

2. 生成式互动聊天
   - 支持基于大语言模型的智能对话
   - 支持多轮上下文理解
   - 支持自定义对话风格和角色设定

3. TTS语音合成
   - 支持多种音色选择
   - 支持语速、音量调节
   - 支持情绪标签设置

4. 捏音功能
   - 支持自定义音色创建
   - 支持性别、风格设置
   - 支持文本风格设置

5. 测试报告
   - 自动生成Excel格式测试报告
   - 支持测试进度实时显示
   - 支持错误信息记录
   - 支持继续执行失败/未执行的测试用例

## 使用方法

### 1. 准备测试用例
在 `data/<测试用例>.xlsx` 文件中编写测试用例，包含以下sheet：
- `realtime`: 实时对话测试用例
- `tts`: TTS测试用例
- `voice`: 捏音测试用例

### 2. 准备音频文件
对于实时语音测试，需要将对应的 **PCM 或 OGG 格式**音频文件放在指定的输入目录（默认为 `data/<测试用例>` 目录）。脚本将根据 `--audio-format` 参数读取相应格式的文件。

### 3. 运行测试
```bash
python run_realtime.py <测试用例>.xlsx --autdio-dir data/<audio>/ [其他选项]
```

选项：
- `<测试用例>.xlsx`   测试用例Excel文件路径（必需）
- `--audio-dir, -a`    音频文件输入目录（默认为与Excel文件同名的目录）
- `--max-history, -m`  历史对话最大保留轮数
- `--config, -c`       配置文件路径
- `--start-id, -s`     指定从哪个ID的用例开始运行
- `--output-dir, -o`   测试报告和输出音频的目录路径（默认为`reports/`）
- `--username, -u`     用户名，用于生成session-id
- `--continue`         如果已存在测试报告，则根据测试报告内容继续执行失败或未执行的用例
- `--audio-format`     输入音频文件的格式 (pcm 或 ogg)，默认为 ogg

#### 3.1 运行测试环境配置
```bash
# 示例
python run_realtime.py data/模板_捏音入参.xlsx -a data/ogg --audio-format ogg -c config/test.env.json
```

### 4. 继续执行失败/未执行的测试用例
当测试执行中断或有部分用例失败时，可以使用`--continue`选项继续执行：

```bash
# 使用原始测试用例文件，程序会自动查找对应的测试报告(<output-dir>/<测试用例>_testReport.xlsx)
python run_realtime.py data/<测试用例>.xlsx --continue

```

程序会智能处理：
- 已成功的用例将被跳过
- 失败的用例将被重新执行
- 未执行的用例将被执行

### 5. 查看结果
测试完成后，结果将保存在 `reports` 目录下，包含：
- 测试报告Excel文件（<用例名>_testReport.xlsx）
- 生成的音频文件

## 配置说明
配置文件支持以下配置项：
- `max_history_turns`: 历史对话最大保留轮数
- `concurrency_limit`: 并发限制
- `realtime.base_url`: 实时语音API地址
- `realtime.token`: API认证token
- `role.base_url`: 角色API地址
- `role.token`: 角色API认证token

## 注意事项
1. 执行实时语音测试前，请确保音频文件已正确放置在指定目录
2. 生成的测试报告与音频文件将保存在 `reports` 目录下
3. 测试用例格式请参考data目录下的模板文件
4. 使用`--continue`参数时，程序会尝试自动查找和使用已有的测试报告文件

## 常见问题
1. 音频文件不存在
   - 检查音频文件是否放在 `--audio-dir` 指定的目录中
   - 检查文件名是否与测试用例中的 transcript 匹配，并且扩展名（`.pcm` 或 `.ogg`）是否与 `--audio-format` 参数指定的格式一致

2. Excel文件格式错误
   - 确保Excel文件包含必要的sheet
   - 检查必填字段是否已填写

3. API连接失败
   - 检查网络连接
   - 验证API token是否正确
   - 确认API地址是否可访问
