#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import argparse

class ModelInfoChecker:
    def __init__(self, trace_id, env, service_type):
        self.trace_id = trace_id
        self.env = env
        self.service_type = service_type
        self.base_urls = {
            'aqta': {
                'test': "https://test-openapi-realtime.c.stepfun-inc.net/realtimeagent/trace/content",
                'prod': "https://openapi-resource.stepfun-inc.com/realtimeagent/trace/content"
            },
            'tts': {
                'test': "https://openapi-trace-debug.c.ibasemind.com/json",
                'prod': "https://openapi-trace-debug.c.ibasemind.com/json"  # 目前两个环境相同，可根据实际情况修改
            }
        }

    def get_url(self, service_type):
        if service_type == 'aqta':
            key = f"tracelog/{self.env}/{self.trace_id}/trace.json"
            return f"{self.base_urls['aqta'][self.env]}?key={key}"
        elif service_type == 'tts':
            return f"{self.base_urls['tts'][self.env]}?trace_id={self.trace_id}"
        else:
            raise ValueError("不支持的服务类型，仅支持 'aqta' 和 'tts'")

    def fetch_model_info(self):
        service_type = self.service_type
        print(f"[DEBUG] 开始获取 {service_type} 链路信息")
        url = self.get_url(service_type)
        try:
            # 发送 GET 请求
            response = requests.get(url)
            print(f"[DEBUG] 发送 GET 请求到: {url}")
            # 检查响应状态码
            response.raise_for_status()
            
            # 解析响应的 JSON 数据
            data = response.json()            
            if service_type == 'aqta':
                # 提取 AQTA 信息
                turn_one_output = data.get('turns', {}).get('1', {}).get('output', {})
                info = {
                    'model': turn_one_output.get('aqta', {}).get('responses', [{}])[0].get('content', {}).get('model'),
                    'temperature': turn_one_output.get('aqta', {}).get('request', {}).get('temperature'),
                    'top_p': turn_one_output.get('aqta', {}).get('request', {}).get('top_p'),
                    'sys_prompt': turn_one_output.get('aqta', {}).get('request', {}).get('messages', [{}])[0].get('content', '').strip() if turn_one_output.get('aqta', {}).get('request', {}).get('messages') else '',
                    'tts_model': turn_one_output.get('tts', {}).get('responses', [{}])[0].get('content', {}).get('data', {}).get('model'),
                    'voice_id': turn_one_output.get('tts', {}).get('voice_id')
                }
                # 处理 sys_prompt 长度
                if len(info['sys_prompt']) > 50:
                    info['sys_prompt'] = info['sys_prompt'][:50] + '...'
            elif service_type == 'tts':
                stepcast_requests = []
                # 从 response 数据中提取 stepcast_request
                for event in data.get('events', []):
                    if 'stepcast_request' in event:
                        stepcast_requests.append(event['stepcast_request'])
                
                if stepcast_requests:
                    output = stepcast_requests[0].get('event', {})
                    info = {
                        'voice_id': output.get('voice_id'),
                        'response_format': output.get('response_format'),
                        'volume_ratio': output.get('volume_ratio'),
                        'speed_ratio': output.get('speed_ratio'),
                        'sample_rate': output.get('sample_rate'),
                        'stream': output.get('stream'),
                        'model': output.get('model'),
                    }
                else:
                    print("[DEBUG] 未找到 stepcast_request 信息")
                    info = {}
            else:
                raise ValueError("不支持的服务类型，仅支持 'aqta' 和 'tts'")
            
            return info
        except requests.RequestException as e:
            print(f"请求发生错误: {e}")
            return None
        except ValueError as e:
            print(f"解析响应数据时出错: {e}")
            return None

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='获取不同模型的信息')
    parser.add_argument('trace_id', type=str, help='追踪 ID')
    parser.add_argument('--service_type', type=str, choices=['aqta', 'tts'], help='服务类型，可选 aqta 或 tts', default='aqta')
    parser.add_argument('--env', type=str, choices=['test', 'prod'], help='环境类型，可选 test 或 prod', default='test')
    args = parser.parse_args()

    checker = ModelInfoChecker(args.trace_id, args.env, args.service_type)

    # 获取 AQTA 或 TTS 信息
    info = checker.fetch_model_info()
    if args.service_type == 'aqta':
        print("AQTA 信息:")
        if info:
            if info['model']:
                print(f"  AQTA模型版本信息: {info['model']}")
            if info['tts_model']:
                print(f"  TTS模型版本信息: {info['tts_model']}")
            if info['voice_id']:
                print(f"  音色版本信息: {info['voice_id']}")
            if info['temperature'] is not None:
                print(f"  温度参数: {info['temperature']}")
            if info['top_p'] is not None:
                print(f"  Top-p 参数: {info['top_p']}")
        else:
            print("  无法获取 AQTA 信息")
    elif args.service_type == 'tts':
        print("TTS 信息:")
        if info:
            if info['voice_id']:
                print(f"  音色: {info['voice_id']}")
            if info['response_format']:
                print(f"  响应格式: {info['response_format']}")
            if info['volume_ratio'] is not None:
                print(f"  音量比例: {info['volume_ratio']}")   
            if info['speed_ratio'] is not None:
                print(f"  语速比例: {info['speed_ratio']}")    
            if info['sample_rate'] is not None:
                print(f"  采样率: {info['sample_rate']}")  
            if info['stream'] is not None:
                print(f"  是否为流式: {info['stream']}")   
        else:
            print("  无法获取 TTS 信息")