import os
import argparse
from openpyxl import load_workbook
from lib_huoshan import ByteDanceTTSClient

FEMAIL_VOICE = "zh_female_linjianvhai_moon_bigtts" # 邻家女孩
MAIL_VOICE = "zh_male_yuanboxiaoshu_moon_bigtts" # 渊博小叔

def main(input_excel, output_dir):
    token = os.getenv('HUOSHAN_TOKEN')

    client = ByteDanceTTSClient(
        appid="9922513467",
        token=token,
        cluster="volcano_tts",
        host="openspeech.bytedance.com"
    )

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 加载 Excel
    wb = load_workbook(input_excel)
    if "tts" not in wb.sheetnames:
        print(f"[Error] Sheet 'tts' not found in {input_excel}")
        return
    sheet = wb["tts"]

    # 获取表头索引
    header = [cell.value for cell in next(sheet.iter_rows(min_row=1, max_row=1))]
    try:
        text_col_index = header.index("text") + 1
        voice_col_index = header.index("音色") + 1
    except ValueError as e:
        print("[Error] Excel 中必须包含 'text' 和 '音色' 列")
        return

    # 遍历每行
    for row_idx, row in enumerate(sheet.iter_rows(min_row=2), start=1):
        text = row[text_col_index - 1].value
        voice_type = row[voice_col_index - 1].value

        if text and voice_type:
            filename = f"ID_{row_idx}.mp3"
            output_path = os.path.join(output_dir, filename)

            if voice_type == 'yinhejingling':
                huoshan_voice_type = FEMAIL_VOICE
            elif voice_type == 'dushexiaoyu':
                huoshan_voice_type = MAIL_VOICE
            else:
                huoshan_voice_type = voice_type
            print(f"\n[Row {row_idx}] 音色: {huoshan_voice_type}, 文本: {text}")
            try:
                client.synthesize(text=text, voice_type=huoshan_voice_type, output_path=output_path)
            except Exception as e:
                print(f"[Error] 合成失败 (Row {row_idx}): {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="从 Excel 读取文本和音色，并合成语音。")
    parser.add_argument("-i", "--input", required=True, help="输入 Excel 文件路径")
    parser.add_argument("-o", "--output", required=True, help="输出音频目录路径")
    args = parser.parse_args()

    main(args.input, args.output)
