import os
import sys
import pandas as pd
from lib_huoshan import ByteDanceTTSClient
import subprocess
import argparse


def load_huoshan_config():
    return (
        os.getenv('HUOSHAN_APPID', '9922513467'),
        os.getenv('HUOSHAN_TOKEN', 'CkV24l9LlaobkXMo_t8AOLijkPzMCxcF'),
        os.getenv('HUOSHAN_VOICE_TYPE', 'zh_female_linjianvhai_moon_bigtts')
    )

def mp3_to_speex_ogg(mp3_path, ogg_path):
    """用ffmpeg将mp3转为speex 16k ogg"""
    cmd = [
        'ffmpeg', '-y', '-i', mp3_path,
        '-ar', '16000', '-ac', '1', '-c:a', 'libspeex', ogg_path
    ]
    subprocess.run(cmd, check=True)

def main():
    parser = argparse.ArgumentParser(description="批量生成语音文件（火山TTS->speex 16k ogg）")
    parser.add_argument('excel', type=str, help='Excel文件路径，包含realtime sheet')
    parser.add_argument('audio_dir', type=str, help='音频输出目录')
    args = parser.parse_args()

    appid, token, voice_type = load_huoshan_config()
    if not appid or not token:
        print(f"[Error] 必须在环境变量中提供appid和token")
        sys.exit(1)

    os.makedirs(args.audio_dir, exist_ok=True)
    df = pd.read_excel(args.excel, sheet_name='realtime', engine='openpyxl')
    tts_client = ByteDanceTTSClient(appid, token)
    for idx, row in df.iterrows():
        transcript = str(row.get('transcript', '')).strip()
        if not transcript:
            continue
        ogg_path = os.path.join(args.audio_dir, f"{transcript}.ogg")
        if os.path.exists(ogg_path):
            print(f"[Skip] {ogg_path} 已存在，跳过。")
            continue
        mp3_path = os.path.join(args.audio_dir, f"{transcript}.mp3")
        print(f"[TTS] 生成 {mp3_path} ...")
        try:
            tts_client.synthesize(transcript, voice_type, mp3_path)
            print(f"[Convert] {mp3_path} -> {ogg_path} (speex 16k)")
            mp3_to_speex_ogg(mp3_path, ogg_path)
            os.remove(mp3_path)
        except Exception as e:
            print(f"[Error] 生成 {transcript} 失败: {e}")
            if os.path.exists(mp3_path):
                os.remove(mp3_path)

if __name__ == '__main__':
    main()
