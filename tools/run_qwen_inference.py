#!/usr/bin/env python3
"""
Usage:
    export QWEN_API_KEY=your_key_here
    python qwen_inference_dashscope_concurrent.py --input your_input.xlsx --workers 4
"""
import os
import argparse
import pandas as pd
from openai import OpenAI
import concurrent.futures

# 初始化 DashScope 客户端
client = OpenAI(
    api_key=os.getenv('QWEN_API_KEY'),
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)
MODEL_NAME = 'qwen2.5-72b-instruct'
SYSTEM_PROMPT = "" # 在这里设置你的系统提示，如果不需要则留空 ""
SHEET_NAME = 'realtime'


def query_qwen_api(messages):
    """
    使用 DashScope client 发送对话消息列表并返回助手回复。
    """
    completion = client.chat.completions.create(
        model=MODEL_NAME,
        messages=messages,
        # temperature=0.0,
        # max_tokens=256,
        stream=False,
    )
    return completion.choices[0].message.content.strip()


def process_group(group_id, group_df):
    """
    处理单个 ID 的所有轮次，按顺序调用 API，返回该组的回复列表。
    """
    print(f"[Start] ID {group_id} with {len(group_df)} turns")
    history = []
    if SYSTEM_PROMPT: # 检查 SYSTEM_PROMPT 是否非空
        history.append({'role': 'system', 'content': SYSTEM_PROMPT})

    replies = []
    for idx, row in group_df.iterrows():
        user_input = row['transcript']
        history.append({'role': 'user', 'content': user_input})
        reply = query_qwen_api(history)
        history.append({'role': 'assistant', 'content': reply})
        replies.append(reply)
        print(f"  ID {group_id} turn {idx - group_df.index[0] + 1}/{len(group_df)} completed")
    print(f"[Done]  ID {group_id}")
    return group_id, replies


def main():
    parser = argparse.ArgumentParser(
        description="Concurrent Qwen2.5-72B-Instruct inference with progress logging."
    )
    parser.add_argument("--input", "-i", required=True, help="Input Excel file path.")
    parser.add_argument("--output_suffix", default="_Qwen_report.xlsx", help="Output suffix.")
    parser.add_argument("--workers", type=int, default=4, help="Parallel workers for IDs.")
    args = parser.parse_args()

    # Load
    df = pd.read_excel(args.input, sheet_name=SHEET_NAME, engine='openpyxl')
    required = {"ID", "turns", "transcript"}
    if not required.issubset(df.columns):
        raise ValueError(f"Missing columns: {required - set(df.columns)}")

    groups = list(df.groupby('ID', sort=False))
    total_ids = len(groups)
    print(f"Processing {total_ids} IDs with {args.workers} workers...")

    id_to_replies = {}
    completed = 0
    with concurrent.futures.ThreadPoolExecutor(max_workers=args.workers) as executor:
        futures = {executor.submit(process_group, gid, gdf): gid for gid, gdf in groups}
        for future in concurrent.futures.as_completed(futures):
            gid = futures[future]
            try:
                _, replies = future.result()
            except Exception as e:
                print(f"[Error] ID {gid}: {e}")
                length = len(df[df['ID'] == gid])
                replies = [None] * length
            id_to_replies[gid] = replies
            completed += 1
            print(f"Progress: {completed}/{total_ids} IDs done")

    # Flatten
    outputs = []
    counters = {gid: 0 for gid, _ in groups}
    for _, row in df.iterrows():
        gid = row['ID']
        idx = counters[gid]
        outputs.append(id_to_replies[gid][idx])
        counters[gid] += 1
    df['Qwen_output_transcript'] = outputs

    # Save
    base, _ = os.path.splitext(args.input)
    out = f"{base}{args.output_suffix}"
    df.to_excel(out, sheet_name=SHEET_NAME, index=False)
    print(f"All done. Output saved to {out}")

if __name__ == "__main__":
    main()
