# coding=utf-8

import ssl
import asyncio
import websockets
import uuid
import json
import gzip

default_header = bytearray(b'\x11\x10\x11\x00')


class ByteDanceTTSClient:
    def __init__(self, appid, token, cluster="volcano_tts", host="openspeech.bytedance.com"):
        self.appid = appid
        self.token = token
        self.cluster = cluster
        self.host = host
        self.api_url = f"wss://{host}/api/v1/tts/ws_binary"
        self.ssl_context = ssl._create_unverified_context()
        self.header = {"Authorization": f"Bearer; {token}"}
        print(f"[Init] appid={appid}, cluster={cluster}, host={host}")

    def _build_request_payload(self, text, voice_type, operation):
        reqid = str(uuid.uuid4())
        request_json = {
            "app": {
                "appid": self.appid,
                "token": "access_token",
                "cluster": self.cluster
            },
            "user": {
                "uid": "2101670807"
            },
            "audio": {
                "voice_type": voice_type,
                "encoding": "mp3",
                "speed_ratio": 1.0,
                "volume_ratio": 1.0,
                "pitch_ratio": 1.0,
            },
            "request": {
                "reqid": reqid,
                "text": text,
                "text_type": "plain",
                "operation": operation
            }
        }

        payload = gzip.compress(json.dumps(request_json).encode("utf-8"))
        message = bytearray(default_header)
        message.extend(len(payload).to_bytes(4, 'big'))
        message.extend(payload)

        print(f"[BuildPayload] operation={operation}, voice_type={voice_type}, reqid={reqid}")
        return message

    async def _send_request(self, message, output_path):
        print(f"[WebSocket] Connecting to {self.api_url} ...")
        try:
            async with websockets.connect(self.api_url, additional_headers=self.header, ping_interval=None, ssl=self.ssl_context) as ws:
                await ws.send(message)
                print(f"[WebSocket] Request sent. Receiving audio to '{output_path}'")
                with open(output_path, "wb") as f:
                    while True:
                        res = await ws.recv()
                        done = self._parse_response(res, f)
                        if done:
                            print("[WebSocket] Audio received completely.")
                            break
        except Exception as e:
            print(f"[Error] WebSocket failed: {e}")
            raise

    def _parse_response(self, res, file):
        message_type = res[1] >> 4
        message_type_specific_flags = res[1] & 0x0f
        message_compression = res[2] & 0x0f
        header_size = res[0] & 0x0f
        payload = res[header_size * 4:]

        if message_type == 0xb:
            if message_type_specific_flags == 0:
                print("[Response] ACK received (no audio)")
                return False
            sequence_number = int.from_bytes(payload[:4], "big", signed=True)
            payload_size = int.from_bytes(payload[4:8], "big", signed=False)
            audio_data = payload[8:]
            file.write(audio_data)
            print(f"[AudioChunk] seq={sequence_number}, size={payload_size}")
            return sequence_number < 0

        elif message_type == 0xf:
            code = int.from_bytes(payload[:4], "big", signed=False)
            msg_size = int.from_bytes(payload[4:8], "big", signed=False)
            error_msg = payload[8:]
            if message_compression == 1:
                error_msg = gzip.decompress(error_msg)
            error_msg = error_msg.decode("utf-8")
            print(f"[Error] Code={code}, Message={error_msg}")
            raise RuntimeError(f"TTS Error {code}: {error_msg}")

        elif message_type == 0xc:
            msg_size = int.from_bytes(payload[:4], "big", signed=False)
            payload = payload[4:]
            if message_compression == 1:
                payload = gzip.decompress(payload)
            print(f"[Frontend] Message={payload.decode('utf-8')}")
            return False
        else:
            print(f"[Warning] Unknown message type: {message_type}")
            return True

    def synthesize(self, text: str, voice_type: str, output_path: str, operation: str = "submit"):
        print(f"[Synthesize] text_len={len(text)}, voice_type={voice_type}, output_path={output_path}")
        print(f"[Text] {text}")
        message = self._build_request_payload(text, voice_type, operation)
        asyncio.run(self._send_request(message, output_path))
