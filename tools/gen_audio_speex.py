import os
import argparse
from openpyxl import load_workbook
from lib_huoshan import ByteDanceTTSClient
import subprocess
import tempfile

def main(input_excel, output_dir, default_voice_type):
    token = os.getenv('HUOSHAN_TOKEN')
    # 初始化 TTS 客户端
    client = ByteDanceTTSClient(
        appid="9922513467",
        token=token,
        cluster="volcano_tts",
        host="openspeech.bytedance.com"
    )

    os.makedirs(output_dir, exist_ok=True)
    wb = load_workbook(input_excel)

    if "realtime" not in wb.sheetnames:
        print(f"[Error] Sheet 'realtime' not found in {input_excel}")
        return

    sheet = wb["realtime"]
    headers = [cell.value for cell in sheet[1]]
    
    try:
        transcript_index = headers.index("transcript")
    except ValueError:
        print("[Error] 'transcript' column not found.")
        return

    voice_type_index = headers.index("音色") if "音色" in headers else None

    for row_idx, row in enumerate(sheet.iter_rows(min_row=2), start=2):
        text_cell = row[transcript_index]
        text = str(text_cell.value).strip() if text_cell.value else ""
        if not text:
            continue

        voice_type = row[voice_type_index].value.strip() if voice_type_index is not None and row[voice_type_index].value else default_voice_type

        # 替换空格为下划线以生成合法文件名
        filename = text.replace(" ", "_") + ".ogg"
        output_path = os.path.join(output_dir, filename)

        if os.path.exists(output_path):
            print(f"[Skip] Already exists: {output_path}")
            continue

        with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as tmp_mp3:
            tmp_mp3_path = tmp_mp3.name

        try:
            print(f"[TTS] Synthesizing: {text} (voice_type: {voice_type})")
            client.synthesize(text=text, voice_type=voice_type, output_path=tmp_mp3_path)

            print(f"[Convert] MP3 -> OGG (Speex 16k): {output_path}")
            cmd = [
                "ffmpeg",
                "-y",
                "-i", tmp_mp3_path,
                "-ar", "16000",
                "-acodec", "libspeex",
                output_path
            ]
            subprocess.run(cmd, check=True)

        except Exception as e:
            print(f"[Error] Processing '{text}' failed: {e}")
        finally:
            if os.path.exists(tmp_mp3_path):
                os.remove(tmp_mp3_path)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="TTS 合成并转 speex 格式")
    parser.add_argument("-i", "--input", required=True, help="输入 Excel 路径")
    parser.add_argument("-o", "--output", required=True, help="输出音频目录")
    parser.add_argument("--voice", default="zh_female_linjianvhai_moon_bigtts", help="默认音色名称（当行中未指定时使用）")
    args = parser.parse_args()

    main(args.input, args.output, args.voice)