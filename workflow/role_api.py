from typing import Dict, Optional
from utils.http_client import HTTPClient

class RoleAPI(HTTPClient):
    def __init__(self, base_url: str, headers: Optional[Dict] = None):
        super().__init__(base_url, headers)
        self.base_url = base_url
        self.headers = headers

    def create_role(self, voice_id: str, name: str, style: str, text_style: str, gender: str) -> Dict:
        """创建基础音色角色"""
        payload = {
            "voice_id": voice_id,
            "name": name,
            "style": style,
            "voice_clone_id": voice_id,
            "text_style": text_style,
            "gender": gender,
        }
        return self.post("/realtimeapi/v1/roles", payload)

    def get_roles(self, skip: int = 0, limit: int = 10) -> Dict:
        """查询角色列表"""
        params = {"skip": skip, "limit": limit}
        return self.get("/realtimeapi/v1/roles", params)

    def get_role_detail(self, role_id: str) -> Dict:
        """查询角色详情"""
        return self.get(f"/realtimeapi/v1/roles/{role_id}")

    def update_role(self, role_id: str, voice_id: str, name: str, style: str) -> Dict:
        """修改角色信息"""
        payload = {
            "voice_id": voice_id,
            "name": name,
            "style": style
        }
        return self.put(f"/realtimeapi/v1/roles/{role_id}", payload)

    def delete_role(self, role_id: str) -> Dict:
        """删除角色"""
        return self.delete(f"/realtimeapi/v1/roles/{role_id}")