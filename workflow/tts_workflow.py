#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import asyncio
import io
from typing import Dict, List, Callable
import base64
from pydub import AudioSegment
from datetime import datetime

from workflow.tts_api import TTSEventHandler
from utils.audio_file_handler import AudioFileHandler
from utils.excel_writer import ExcelWriter
from utils.session_manager import SessionManager
from utils.config_manager import get_config_manager

class TTSWorkflow:
    """TTS工作流，处理文本到语音的转换"""

    def __init__(self, concurrency_limit=1, tts_mode='sentence'):
        """初始化TTS工作流

        Args:
            concurrency_limit: 并发限制，默认为1
            tts_mode: TTS文本发送模式，'sentence'或'slice'
        """
        self.concurrency_limit = concurrency_limit
        self.tts_mode = tts_mode

    def get_pcm_from_mp3(self, audio_path: str) -> bytes:
        """从MP3文件获取PCM数据

        Args:
            audio_path: MP3文件路径

        Returns:
            bytes: PCM格式的音频数据
        """
        audio_file = AudioSegment.from_mp3(audio_path)
        pcm_io = io.BytesIO()
        audio_file.export(pcm_io, format="s16le", codec="pcm_s16le")
        pcm_data = pcm_io.getvalue()
        pcm_io.close()
        return pcm_data

    def get_ogg_from_mp3(self, audio_path: str) -> bytes:
        """从MP3文件获取OGG数据

        Args:
            audio_path: MP3文件路径

        Returns:
            bytes: OGG格式的音频数据
        """
        audio_file = AudioSegment.from_mp3(audio_path)
        ogg_io = io.BytesIO()
        # Ogg Speex 编码
        audio_file.export(ogg_io, format="ogg", codec="libspeex")
        ogg_data = ogg_io.getvalue()
        ogg_io.close()
        return ogg_data

    async def get_tts_audio(self, test_case: Dict, audio_output_dir: str, excel_file: str = None, row_index: int = None, tts_mode: str = None) -> None:
        """获取TTS音频

        Args:
            test_case: TTS测试用例
            audio_output_dir: 自定义输出目录
            excel_file: Excel文件路径，用于保存session_id
            row_index: Excel行索引，用于保存session_id
            tts_mode: TTS文本发送模式，'sentence'或'slice'
        """
        print("\n[DEBUG] 开始生成TTS音频")
        # 确保 text 一定是字符串类型
        test_case['text'] = str(test_case.get('text', ''))
        print(f"[DEBUG] TTS参数: voice_id={test_case.get('voice_id', 'N/A')}, "
              f"speed_ratio={test_case.get('speed_ratio', 1.0)}, "
              f"volume_ratio={test_case.get('volume_ratio', 1.0)}, "
              f"emotion={test_case.get('emotion', '')}, "
              f"language={test_case.get('language', '')}")
        print(f"[DEBUG] 文本内容: {test_case['text'][:50]}{'...' if len(test_case['text']) > 50 else ''}")

        semaphore_tts = asyncio.Semaphore(self.concurrency_limit)
        async with semaphore_tts:
            token = get_config_manager().get("tts.token")
            additional_headers = {"Authorization": f"Bearer {token}"}

            # 提前生成session_id
            username = getattr(self, 'username', None)  # 假设存在username属性，若不存在需调整
            session_id = SessionManager.create_session_id(username)
            print(f"[DEBUG] TTS生成session_id: {session_id}")
            
            # 设置session_id到headers中
            additional_headers['Session-ID'] = session_id


            handler = TTSEventHandler(
                base_url=get_config_manager().get("tts.base_url"),
                headers=additional_headers
            )

            audio_chunks = []
            def audio_callback(data):
                audio_chunks.append(data)
                print(f"[DEBUG] 收到音频数据块，大小: {len(data)} 字节")

            try:
                print("[DEBUG] 连接到TTS服务器...")
                # 处理emotion参数
                emotion = test_case.get('emotion', '')
                # 检查emotion参数的格式
                if emotion and isinstance(emotion, str):
                    emotion = emotion.strip()
                    print(f"[DEBUG] 使用emotion参数: {emotion}")
                else:
                    emotion = ''
                    print("[DEBUG] 不使用emotion参数")

                # 处理language参数
                language = test_case.get('language', '')
                # 检查language参数的格式
                if language and isinstance(language, str):
                    language = language.strip()
                    print(f"[DEBUG] 使用language参数: {language}")
                else:
                    language = ''
                    print("[DEBUG] 不使用language参数")

                response = await handler.connect(voice_id=test_case['voice_id'],
                    speed_ratio=test_case.get('speed_ratio', 1.0),
                    volume_ratio=test_case.get('volume_ratio', 1.0),
                    emotion=emotion,  # 传递处理后的emotion参数
                    language=language,  # 传递处理后的language参数
                    tts_mode=tts_mode or self.tts_mode)

                print("[DEBUG] 发送文本到TTS服务器...")
                await handler.send_text(test_case['text'])
                print("[DEBUG] 等待接收音频数据...")
                tts_first_word_time = await handler.receive_audio(audio_callback)

                # 保存TTS首字响应时间
                if tts_first_word_time is not None:
                    test_case['tts_first_word_time'] = tts_first_word_time
                    tts_first_word_time_ms = tts_first_word_time * 1000
                    print(f"[DEBUG] 记录TTS首字响应时间: {tts_first_word_time_ms:.0f}")

                if audio_chunks:
                    print("[DEBUG] 保存TTS音频文件...")
                    audio_path = AudioFileHandler.save_audio_file(
                        audio_data=audio_chunks[0],
                        test_case=test_case,
                        test_type='tts',
                        turn_id=test_case["turns"],
                        audio_output_dir=audio_output_dir
                    )
                    test_case['audio_path'] = audio_path
                    print(f"[DEBUG] TTS音频已保存到: {audio_path}")

                    # 如果需要保存session_id，添加到test_case中供调用者使用
                    if handler.session_id:
                        test_case['session_id'] = handler.session_id
                else:
                    print("[DEBUG] 警告: 未收到音频数据")

            except Exception as e:
                print(f"TTS生成失败: {str(e)}")
                raise
            finally:
                print("[DEBUG] 关闭TTS连接")
                await handler.close()
