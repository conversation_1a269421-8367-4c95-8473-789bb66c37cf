#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import yaml
from typing import Dict, List
from openai import OpenAI

class ChatWorkflow:
    """生成式闲聊工作流，处理对话生成"""
    
    def __init__(self, model, api_key, base_url, prompt_config='config/prompt_config.yaml'):
        """初始化生成式闲聊工作流
        
        Args:
            model: 使用的模型，默认为"doubao"
            api_key: API密钥，默认从环境变量获取
            base_url: API基础URL
        """
        self.model = model
        self.api_key = api_key
        self.base_url = base_url
        with open(prompt_config, 'r', encoding='utf-8') as f:
            self.prompt_config = yaml.safe_load(f)
        
    def get_chat_query(self, messages: List[Dict], case_keyword: str=None, parameter: str=None, opening_words: str=None) -> str:
        """获取生成式闲聊的回复
        
        Args:
            messages: 对话历史列表
            case_keyword: 用例关键词，用于选择提示词模板
            parameter: 替换提示词中的{parameter}占位符
            opening_words: 替换提示词中的{opening_words}占位符
            
        Returns:
            str: 生成的回复文本
        """
        print("\n[DEBUG] 调用大模型API生成闲聊内容")
        client = OpenAI(
            api_key=self.api_key,  # 混元 APIKey
            base_url=self.base_url  # 混元 endpoint
        )
        print(f"[DEBUG] 使用API密钥: {self.api_key[:8]}...{self.api_key[-8:]}")
        print(f"[DEBUG] 使用模型: {self.model}, API地址: {self.base_url}")
        
        # 获取系统提示词并替换占位符
        system_prompt = self.prompt_config['prompts'].get(case_keyword, {}).get('system', '''
        一个提问小能手
        以下要求 :
        1. 回复格式为纯文本
        2. 根据以往对话来回答或提出问题。
        3. 注意输出长度限制为100字，避免生成过长的内容。
        ''')
        
        # 替换占位符
        system_prompt = system_prompt.replace('{parameter}', parameter)
        system_prompt = system_prompt.replace('{opening_words}', opening_words)
                    
        msgs = [{
            "role": "system",
            "content": system_prompt
        }]
        
        print("[DEBUG] system prompt:")
        for msg in msgs:
            print(f"  role: {msg['role']}")
            print(f"  content: {msg['content']}")
        
        print("[DEBUG] 处理对话历史:")
        for i, msg in enumerate(messages):
            if msg["role"] == "user":
                msgs.append({"role": "assistant", "content": msg['text']})
                print(f"  历史[{i+1}] user -> assistant: {msg['text'][:30]}{'...' if len(msg['text']) > 30 else ''}")
            elif msg["role"] == "assistant":
                msgs.append({"role": "user", "content": msg['text']})
                print(f"  历史[{i+1}] assistant -> user: {msg['text'][:30]}{'...' if len(msg['text']) > 30 else ''}")
            
        print("[DEBUG] 发送请求到模型API...")
        completion = client.chat.completions.create(
            model=self.model, 
            messages=msgs
        )
        result = completion.choices[0].message.content
        print(f"[DEBUG] 收到回复: {result[:50]}{'...' if len(result) > 50 else ''}")
        return result


