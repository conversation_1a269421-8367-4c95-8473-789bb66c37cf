import sys
import os
import re
import asyncio

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.websocket_client import WebSocketClient
import json
import base64


def split_text_with_punctuation(text):
    """
    使用指定的切句符号进行文本切分
    定义切句符号（按长度从长到短排序）
    """
    punctuations = [
        r'\$-\$', r'\|', r'~', r'～', r'\n', r'：', r'>', r'、',
        r'"', r'"', r'！', r'？', r'，', r'。', r'!', r'\?', r':'
    ]

    # 构建正则表达式：用 lookahead 保留标点符号在当前句末
    pattern = f"(.*?(?:{'|'.join(punctuations)}))"

    # 使用 re.findall 获取所有匹配片段
    matches = re.findall(pattern, text)

    # 找出剩余内容（如果最后没有标点，也包含进去）
    remainder = re.sub(pattern, '', text)
    if remainder.strip():
        matches.append(remainder)

    return [s for s in matches if s.strip()]


class TTSEventHandler:
    def __init__(self, base_url, headers=None, tts_mode='slice'):
        self.base_url = base_url  # 保存API地址
        self.headers = headers or {}
        self.client = None
        self.session_id = None
        self.event_id = None
        self.tts_mode = tts_mode

    async def connect(self, voice_id, response_format='mp3_stream', speed_ratio=1.0, volume_ratio=1.0, sample_rate=24000, emotion=None, language=None):
        # 处理 voice_label 参数（情绪和语言/方言）
        voice_label = {}
        if emotion and language:
            print(f"[DEBUG] 警告: tts接口不支持情绪和语言和方言参数同时存在: {emotion}, {language}")
        if emotion:
            voice_label["emotion"] = emotion
        if language:
            voice_label["language"] = language

        # 构建查询参数
        query_params = {
            "role_id": voice_id,
            "response_format": response_format,
            "speed_ratio": speed_ratio,
            "volume_ratio": volume_ratio,
            "mode": 'default' if self.tts_mode == 'slice' else 'sentence',
            "sample_rate": sample_rate
        }
        if voice_label:
            query_params["voice_label"] = ':'.join([f'{k}:{v}' for k, v in voice_label.items()])

        # 构建带查询参数的 URL
        query_string = '&'.join([f'{k}={v}' for k, v in query_params.items()])
        url_with_query = f'{self.base_url}?{query_string}'

        print(f"\n[DEBUG] 连接到TTS API: {url_with_query}")
        self.client = WebSocketClient(url_with_query, self.headers)
        await self.client.connect()
        connection_response = await self.client.receive()
        print(f"[DEBUG] TTS连接响应: {connection_response}")
        if connection_response.get('type') == 'tts.connection.done':
            self.session_id = str(connection_response['data']['session_id'])
            print(f"[DEBUG] TTS会话创建成功，session_id: {self.session_id}")
        else:
            raise ConnectionError("WebSocket连接失败")

    async def send_text(self, text):
        # 根据tts_mode决定发送方式
        if getattr(self, 'tts_mode', 'sentence') == 'slice':
            # 使用新的切句函数进行文本切分
            slices = split_text_with_punctuation(text)
            print(f"[DEBUG] 文本切分为 {len(slices)} 个片段")

            for idx, piece in enumerate(slices):
                text_payload = {
                    "type": "tts.text.delta",
                    "data": {
                        "session_id": self.session_id,
                        "text": piece
                    }
                }
                print(f"\n[DEBUG] 发送给TTS API: {self.base_url}")
                print(f"[DEBUG] 发送文本切片({idx + 1}/{len(slices)}): {piece[:50]}{'...' if len(piece) > 50 else ''}")
                await self.client.send(text_payload)
        else:
            # sentence模式，原有逻辑
            text_payload = {
                "type": "tts.text.delta",
                "data": {
                    "session_id": self.session_id,
                    "text": text
                }
            }
            print(f"\n[DEBUG] 发送给TTS API: {self.base_url}")
            print(f"[DEBUG] 发送文本内容: {text[:50]}{'...' if len(text) > 50 else ''}")
            await self.client.send(text_payload)

        done_payload = {
            "type": "tts.text.done",
            "data": {"session_id": self.session_id}
        }
        print("[DEBUG] 发送文本完成信号")
        await self.client.send(done_payload)

    async def receive_audio(self, callback):
        print(f"\n[DEBUG] 等待来自TTS API: {self.base_url} 的音频响应")
        start_time = asyncio.get_event_loop().time()
        first_audio_time = None

        while True:
            response = await self.client.receive()
            print(f"[DEBUG] 收到TTS响应类型: {response['type']}")

            # 检查是否是流式音频响应
            if response['type'] == 'tts.response.audio.delta':
                # 记录首字响应时间（第一次收到音频数据时）
                if first_audio_time is None:
                    current_time = asyncio.get_event_loop().time()
                    first_audio_time = current_time - start_time
                    first_audio_time_ms = first_audio_time * 1000
                    print(f"TTS首字响应时间: {first_audio_time_ms:.0f}")
            elif response['type'] == 'tts.response.audio.done':
                self.event_id = str(response['event_id'])
                print(f"[DEBUG] TTS音频生成完成，event_id: {self.event_id}")
                audio_data = base64.b64decode(response['data']['audio'])
                print(f"[DEBUG] 收到音频数据长度: {len(audio_data)} 字节")
                callback(audio_data)
                break

        return first_audio_time

    async def close(self):
        if self.client:
            await self.client.close()
