#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import base64
import json
import random
import pandas as pd
import asyncio
from datetime import datetime
from typing import Dict, List, Tuple, Optional

from workflow.realtime_api import RealtimeEventHandler
from workflow.role_api import Role<PERSON>I
from utils.audio_file_handler import AudioFileHandler
from utils.excel_writer import ExcelWriter
from utils.test_status import TestStatus
from utils.session_manager import SessionManager
from utils.data_handler import clean_string


class ServerEvent:
    """服务器事件类型"""
    ERROR = "error"
    CONVERSATION_ITEM_CREATED = "conversation.item.created"
    RESPONSE_CREATED = "response.created"
    RESPONSE_DONE = "response.done"
    RESPONSE_AUDIO_DELTA = "response.audio.delta"
    RESPONSE_AUDIO_DONE = "response.audio.done"
    RESPONSE_AUDIO_TRANSCRIPT_DELTA = "response.audio_transcript.delta"
    RESPONSE_AUDIO_TRANSCRIPT_DONE = "response.audio_transcript.done"


class VoiceCreateWorkflow:
    """捏音和保存角色工作流"""

    def __init__(self, voice_api_client: RealtimeEventHandler, role_api_client: RoleAPI):
        """初始化捏音和保存角色工作流

        Args:
            voice_api_client: 捏音API客户端
            role_api_client: 角色API客户端
        """
        self.voice_api_client = voice_api_client
        self.role_api_client = role_api_client
        self.writer = ExcelWriter()

    async def create_voice(self, test_case: Dict) -> Tuple[Dict, bytes, str, str, Optional[float]]:
        """执行捏音操作

        Args:
            test_case: 捏音测试用例

        Returns:
            Tuple[Dict, bytes, str, str, Optional[float]]: 响应、音频数据、转录文本、会话ID、首字响应时间
        """
        print(f"\n[DEBUG] 执行捏音操作: {test_case}")
        
        # 确保 text 一定是字符串
        test_case['text'] = str(test_case.get('text', ''))
        
        # 提前生成session_id
        username = getattr(self.voice_api_client, 'username', None)
        session_id = SessionManager.create_session_id(username)
        print(f"[DEBUG] 捏音生成session_id: {session_id}")
        
        # 设置session_id到handler的headers中
        self.voice_api_client.headers['Session-ID'] = session_id

        # 连接WebSocket
        await self.voice_api_client.connect()

        # 发送捏音请求
        # 处理style和text_style字段
        style = clean_string(test_case.get('style'))
        text_style = clean_string(test_case.get('text_style'))

        # 处理messages字段
        messages = clean_string(test_case.get('messages'))
        voice_styel_query_template = [
            "说话{style}一些",
            "能不能{style}一些",
            "用{style}的语气和我说句话",
            "能不能用{style}的语气和我说句话",
            "语气{style}点"
        ]
        
        if not messages:
            messages = random.choice(voice_styel_query_template).format(style=style)
            test_case['messages'] = messages

        print(f"[DEBUG] 捏音请求参数: voice_id={test_case['voice_id']}, style={style}, text_style={text_style}, messages={messages[:50]}{'...' if len(messages) > 50 else ''}")

        await self.voice_api_client.create_voice(
            base_voice_id=test_case['voice_id'],
            style=style,
            text_style=text_style,
            messages=messages,
        )

        # 接收响应
        audio_chunks = b""
        transcript_chunks = []
        full_transcript = ""
        audio_data = None
        audio_complete = False
        transcript_complete = False
        voice_first_word_time = None  # 首字响应时间

        start_time = asyncio.get_event_loop().time()

        while True:
            response = await self.voice_api_client.receive()
            try:
                response_type: str = response.get('type')
                print(f"[DEBUG] 捏音响应类型: {response_type}")
            except json.JSONDecodeError as e:
                print(f"JSON解析失败 原始数据: {response}")
                raise e

            if response_type == ServerEvent.RESPONSE_CREATED:
                continue
            elif response_type == ServerEvent.RESPONSE_AUDIO_DELTA:
                # 记录首字响应时间（第一次收到音频数据时）
                if voice_first_word_time is None:
                    current_time = asyncio.get_event_loop().time()
                    voice_first_word_time = current_time - start_time
                    voice_first_word_time_ms = voice_first_word_time * 1000
                    print(f"Voice首字响应时间: {voice_first_word_time_ms:.0f}")

                audio_chunks += base64.b64decode(response['delta'])
            elif response_type == ServerEvent.RESPONSE_AUDIO_TRANSCRIPT_DELTA:
                delta_content = response['delta']
                if delta_content != '\x00': # 检查 delta 是否为 '\x00'
                    transcript_chunks.append(delta_content)
            elif response_type == ServerEvent.RESPONSE_AUDIO_DONE:
                audio_data = audio_chunks
                if audio_data:
                    audio_complete = True
            elif response_type == ServerEvent.RESPONSE_AUDIO_TRANSCRIPT_DONE:
                transcript_complete = True
                full_transcript = ''.join(transcript_chunks)
                print('完整转录文本:', full_transcript)
            elif response_type == ServerEvent.RESPONSE_DONE:
                if not audio_complete or not transcript_complete:
                    raise ValueError("未收到完整的音频或转录数据")
                break
            elif response_type == ServerEvent.ERROR:
                raise ValueError(f"捏音失败: {response.get('error_message', '捏音响应类型: error')}")
            else:
                print(f"未知响应类型: {response_type}")

        # 关闭连接
        await self.voice_api_client.close()

        return response, audio_data, full_transcript, session_id, voice_first_word_time

    async def process_voice_cases(self, voice_case_list: List[Dict], case: Dict, excel_file: str, row_index: int, case_indices: List[int], cases: List[Dict], index: int, audio_output_dir: str) -> bool:
        """处理多个捏音用例

        Args:
            voice_case_list: 捏音用例列表
            case: 当前测试用例
            excel_file: Excel文件路径
            row_index: 当前行索引 (realtime sheet)
            case_indices: 所有行索引列表 (realtime sheet)
            cases: 所有测试用例
            index: 当前用例索引
            audio_output_dir: 自定义输出目录

        Returns:
            bool: 是否至少有一个捏音操作成功并且角色保存成功
        """
        print(f"\n[DEBUG] 找到ID为{case['ID']}的捏音用例: {len(voice_case_list)}个")

        # 保存所有音频文件的路径
        audio_paths = []
        all_transcripts = []
        voice_results = []  # 保存每个捏音用例的结果，包括session_id
        response = None # 保存最后一个response，用于获取event_id（如果需要的话）

        # 遍历所有捏音用例
        for voice_idx, voice_case in enumerate(voice_case_list):
            print(f"\n[DEBUG] 处理第 {voice_idx + 1}/{len(voice_case_list)} 个捏音用例: {voice_case}")

            # 创建捏音用例
            voice_create_case = {
                'ID': voice_case['ID'],
                'voice_id': voice_case['voice_id'],
                'style': clean_string(voice_case.get('style')),
                'text_style': clean_string(voice_case.get('text_style')),
                'messages': clean_string(voice_case.get('messages'))
            }

            # 执行捏音操作
            print(f"2.{voice_idx + 1} 执行捏音操作")

            try:
                response, audio_data, transcript_text, session_id, voice_first_word_time = await self.create_voice(voice_create_case)

                # 保存音频文件
                if audio_data:
                    case_with_idx = case.copy()
                    case_with_idx['voice_idx'] = voice_idx + 1
                    audio_path = AudioFileHandler.save_audio_file(
                        audio_data=audio_data,
                        test_case=case_with_idx,
                        test_type='voice',
                        turn_id=f"{voice_idx + 1}",
                        audio_output_dir=audio_output_dir
                    )
                    print(f"\n[DEBUG] 捏音音频文件已保存到: {audio_path}")

                    # 添加到音频路径列表
                    audio_paths.append(audio_path)
                    all_transcripts.append(transcript_text)

                    # 保存每个捏音用例的结果，包含所有相关信息
                    voice_results.append({
                        'voice_case': voice_case,
                        'audio_mp3': audio_path,
                        'transcript': transcript_text,
                        'session_id': session_id,
                        'event_id': response.get('event_id', ''), # 保存每个用例的event_id
                        'voice_first_word_time': voice_first_word_time  # 保存首字响应时间
                    })

            except Exception as e:
                print(f"\n[DEBUG] 第 {voice_idx + 1} 个捏音操作失败: {str(e)}")
                # 记录失败状态到voice_results，以便后续写入Excel
                voice_results.append({
                    'voice_case': voice_case,
                    'audio_mp3': '',
                    'transcript': '',
                    'session_id': None,
                    'event_id': '',
                    'status': TestStatus.FAILED,
                    'error_message': str(e),
                    'voice_first_word_time': None
                })
                continue

        # 无论成功失败，都尝试写入结果到 voice sheet
        try:
            # 遍历所有捏音结果，更新对应的voice sheet行
            for result in voice_results:
                voice_case_data = result['voice_case'] # 从 result 中获取 voice_case
                audio_path = result['audio_mp3']
                transcript = result['transcript']
                session_id = result['session_id']
                event_id = result['event_id']
                status = result.get('status', TestStatus.SUCCESS) # 默认为成功
                error_message = result.get('error_message', '')
                voice_first_word_time = result.get('voice_first_word_time')

                # 直接使用传递过来的原始行索引
                original_voice_sheet_row_index = voice_case_data.get('original_voice_sheet_row_index')

                if original_voice_sheet_row_index is None:
                    print(f"[警告] 捏音用例 {voice_case_data.get('ID')} 缺少 original_voice_sheet_row_index，无法更新 voice sheet 中的结果。跳过此条记录。")
                    continue
                
                # 准备要写入的数据
                data_to_write = {
                    'output_transcript': transcript,
                    'status': str(status),
                    'test_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'audio_mp3': audio_path,
                    'event_id': event_id,
                    'error_message': error_message
                }

                # 添加voice首字响应时间
                if voice_first_word_time is not None:
                    voice_first_word_time_ms = voice_first_word_time * 1000
                    data_to_write['voice首字响应时间'] = f"{voice_first_word_time_ms:.0f}"
                    print(f"[DEBUG] 记录Voice首字响应时间: {voice_first_word_time_ms:.0f}")
                else:
                    data_to_write['voice首字响应时间'] = ''
                
                # 如果有session_id，创建URL并添加到数据中
                if session_id: # 确保 session_id 存在才创建 URL
                    session_url = SessionManager.create_session_url(session_id)
                    data_to_write['session'] = session_url
                else:
                    data_to_write['session'] = '' # 如果 session_id 为 None，则写入空字符串
                
                # 更新voice sheet中的数据，使用原始行索引
                # 注意：ExcelWriter 的 write_to_excel 期望的行索引是基于0的，
                # 而原始的 voice_df.iterrows() 返回的索引也是基于0的，所以可以直接使用。
                # 如果 ExcelWriter 期望1基索引，则需要 original_voice_sheet_row_index + 1。
                # 假设 ExcelWriter 使用0基索引。
                self.writer.write_to_excel(
                    excel_path=excel_file,
                    sheet_name='voice',
                    data_dict={original_voice_sheet_row_index: data_to_write} 
                )
                print(f"[DEBUG] 已将捏音结果写入voice sheet, 行索引(0-based): {original_voice_sheet_row_index}, Session ID: {session_id}, Status: {status}")
        except Exception as e:
            print(f"[警告] 处理或写入voice sheet时出错: {str(e)}")

        # 如果有成功的操作，才执行保存角色和更新realtime sheet的role_id
        # 检查voice_results中是否有成功的条目
        has_success = any(result.get('status', TestStatus.SUCCESS) == TestStatus.SUCCESS for result in voice_results)
        
        if has_success:
             # 将realtime sheet中对应的行的状态标记为成功 (只标记状态和时间)
             # 注意：这里不再写入 audio_mp3, output_transcript, event_id, messages
            self.writer.write_to_excel(
                excel_path=excel_file,
                sheet_name='realtime',
                data_dict={
                    row_index: {
                        'status': str(TestStatus.SUCCESS),
                        'test_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    }
                }
            )
            
            # 执行完所有捏音用例后自动执行保存角色操作 (只有在至少一次成功时执行)
            if voice_case_list:
                print("3. 执行保存角色操作")
                # 使用最后一个voice_case进行保存角色操作
                last_voice_case = voice_case_list[-1]
                role = self.save_role(
                    voice_id=last_voice_case['voice_id'],
                    name=last_voice_case['name'],
                    style=clean_string(last_voice_case.get('style')),
                    text_style=clean_string(last_voice_case.get('text_style')),
                    gender=clean_string(last_voice_case.get('gender'))
                )

                if role:
                    print(f"\n[DEBUG] 角色保存成功，role_id: {role['role_id']}")
                    # 更新role_id
                    self.writer.write_to_excel(
                        excel_path=excel_file,
                        sheet_name='realtime',
                        data_dict={
                            row_index: {
                                'role_id': role['role_id']
                            }
                        }
                    )

                    # 更新当前用例组的所有后续用例的role_id
                    for next_index in range(index + 1, len(cases)):
                        next_row_index = case_indices[next_index]
                        self.writer.write_to_excel(
                            excel_path=excel_file,
                            sheet_name='realtime',
                            data_dict={
                                next_row_index: {
                                    'role_id': role['role_id']
                                }
                            }
                        )
                        # 更新内存中的用例
                        cases[next_index]['role_id'] = role['role_id']
                else:
                    # 角色保存失败，更新realtime sheet的状态
                    print("\n[DEBUG] 角色保存失败")
                    self.writer.write_to_excel(
                        excel_path=excel_file,
                        sheet_name='realtime',
                        data_dict={
                            row_index: {
                                'status': str(TestStatus.FAILED),
                                'error_message': '角色保存失败'
                            }
                        }
                    )
            return True # 返回成功状态，因为至少有一个捏音成功
        else:
            # 如果所有捏音操作都失败了
            print(f"[DEBUG] ID为{case['ID']}的所有捏音操作均失败")
            # 更新realtime sheet的状态为失败
            self.writer.write_to_excel(
                excel_path=excel_file,
                sheet_name='realtime',
                data_dict={
                    row_index: {
                        'status': str(TestStatus.FAILED),
                        'error_message': '所有捏音操作失败',
                        'test_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        'audio_mp3': '', # 确保清空这些字段
                        'output_transcript': '',
                        'event_id': '',
                        'messages': ''
                    }
                }
            )
            return False

    def save_role(self, voice_id: str, name: str, style: str, text_style: str, gender: str) -> Optional[Dict]:
        """保存角色

        Args:
            voice_id: 音色ID
            name: 角色名称
            style: 风格
            text_style: 文本风格
            gender: 性别

        Returns:
            Optional[Dict]: 保存的角色信息，失败时返回None
        """
        # Clean parameters at the beginning of the method for robustness
        voice_id_cleaned = clean_string(voice_id)
        name_cleaned = clean_string(name)
        style_cleaned = clean_string(style)
        text_style_cleaned = clean_string(text_style)
        gender_cleaned = clean_string(gender)

        print(f"\n[DEBUG] 保存角色 (cleaned): voice_id={voice_id_cleaned}, name={name_cleaned}, style={style_cleaned}, text_style={text_style_cleaned}, gender={gender_cleaned}")

        try:
            role = self.role_api_client.create_role(
                voice_id=voice_id_cleaned,
                name=name_cleaned,
                style=style_cleaned,
                text_style=text_style_cleaned,
                gender=gender_cleaned,
            )

            # 检查响应中是否包含错误信息
            if 'error' in role:
                print(f"[DEBUG] 角色保存失败: {role}")
                print("保存角色失败")
                return None
            else:
                print(f"[DEBUG] 角色保存成功: {role}")
                return role
        except Exception as e:
            print(f"创建角色时发生异常: {str(e)}")
            print(f"请求参数 (cleaned): voice_id={voice_id_cleaned}, name={name_cleaned}, style={style_cleaned}, text_style={text_style_cleaned}, gender={gender_cleaned}")
            import traceback
            print(f"堆栈信息: {traceback.format_exc()}")
            return None
