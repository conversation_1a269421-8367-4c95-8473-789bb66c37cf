# Vocal Auto Web Client 云端部署指南

## 📋 目录

- [系统要求](#系统要求)
- [快速部署](#快速部署)
- [详细配置](#详细配置)
- [部署方式](#部署方式)
- [监控和维护](#监控和维护)
- [故障排除](#故障排除)

## 🔧 系统要求

### 最低配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 20GB 可用空间
- **操作系统**: Linux (Ubuntu 20.04+, CentOS 8+)

### 推荐配置
- **CPU**: 4核心
- **内存**: 8GB RAM
- **存储**: 50GB SSD
- **网络**: 稳定的互联网连接

### 软件依赖
- Docker 20.10+
- Docker Compose 2.0+
- curl (用于健康检查)

## 🚀 快速部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd vocal-auto
```

### 2. 配置环境
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
vim .env
```

### 3. 一键部署
```bash
# 给部署脚本执行权限
chmod +x deploy.sh

# 执行部署
./deploy.sh test deploy
```

### 4. 验证部署
```bash
# 检查服务状态
./deploy.sh test status

# 访问Web界面
curl http://localhost:5000
```

## ⚙️ 详细配置

### 环境配置文件 (.env)

```bash
# 应用配置
FLASK_ENV=production
SECRET_KEY=your-secret-key-here

# API配置
TTS_TOKEN=your-tts-token
REALTIME_TOKEN=your-realtime-token
ROLE_TOKEN=your-role-token

# 端口配置
WEB_PORT=5000
NGINX_HTTP_PORT=80
```

### API配置文件

编辑 `config/test.env.json` 或 `config/uat.env.json`:

```json
{
    "tts": {
        "base_url": "wss://your-tts-api.com/v1/realtime-tts",
        "token": "your-tts-token"
    },
    "realtime": {
        "base_url": "wss://your-realtime-api.com/v1/realtime-server",
        "token": "your-realtime-token"
    }
}
```

## 🐳 部署方式

### 方式一: Docker Compose (推荐)

```bash
# 构建并启动
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 方式二: 单独Docker容器

```bash
# 构建镜像
docker build -t vocal-auto-web .

# 运行容器
docker run -d \
  --name vocal-auto-web \
  -p 5000:5000 \
  -v $(pwd)/config:/app/config:ro \
  -v $(pwd)/data:/app/data \
  vocal-auto-web
```

### 方式三: 使用部署脚本

```bash
# 查看帮助
./deploy.sh

# 构建镜像
./deploy.sh test build

# 启动服务
./deploy.sh test start

# 重启服务
./deploy.sh test restart

# 查看日志
./deploy.sh test logs

# 停止服务
./deploy.sh test stop
```

## 📊 监控和维护

### 健康检查

```bash
# 检查Web服务
curl -f http://localhost:5000/

# 检查容器状态
docker-compose ps

# 查看资源使用
docker stats
```

### 日志管理

```bash
# 查看应用日志
docker-compose logs vocal-auto-web

# 查看Nginx日志
docker-compose logs nginx

# 实时日志
docker-compose logs -f --tail=100
```

### 数据备份

```bash
# 手动备份
./deploy.sh test backup

# 自动备份 (添加到crontab)
0 2 * * * /path/to/vocal-auto/deploy.sh test backup
```

### 更新部署

```bash
# 拉取最新代码
git pull

# 重新构建并部署
./deploy.sh test deploy

# 或者分步执行
./deploy.sh test build
./deploy.sh test restart
```

## 🔒 安全配置

### SSL/HTTPS配置

1. 获取SSL证书
2. 将证书放在 `ssl/` 目录
3. 修改 `nginx.conf` 启用HTTPS配置
4. 重启Nginx服务

### 防火墙配置

```bash
# 开放必要端口
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 5000/tcp  # 仅开发环境
```

## 🛠️ 故障排除

### 常见问题

#### 1. 容器启动失败
```bash
# 查看详细错误
docker-compose logs vocal-auto-web

# 检查配置文件
docker-compose config
```

#### 2. 端口冲突
```bash
# 检查端口占用
netstat -tulpn | grep :5000

# 修改端口配置
vim docker-compose.yml
```

#### 3. 权限问题
```bash
# 修复文件权限
sudo chown -R $USER:$USER .
chmod +x deploy.sh
```

#### 4. 内存不足
```bash
# 检查内存使用
free -h
docker stats

# 清理Docker资源
./deploy.sh test cleanup
```

### 性能优化

#### 1. 调整容器资源限制
```yaml
# docker-compose.yml
services:
  vocal-auto-web:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
```

#### 2. 启用Nginx缓存
```nginx
# nginx.conf
location ~* \.(js|css|png|jpg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

## 📞 技术支持

如果遇到部署问题，请：

1. 检查系统要求是否满足
2. 查看错误日志: `./deploy.sh test logs`
3. 验证配置文件格式
4. 检查网络连接和API可用性

## 🔄 版本更新

### 更新流程
1. 备份当前数据: `./deploy.sh test backup`
2. 拉取最新代码: `git pull`
3. 重新构建: `./deploy.sh test build`
4. 重启服务: `./deploy.sh test restart`
5. 验证功能: `./deploy.sh test status`

### 回滚操作
```bash
# 回滚到上一个版本
git checkout <previous-commit>
./deploy.sh test deploy
```
