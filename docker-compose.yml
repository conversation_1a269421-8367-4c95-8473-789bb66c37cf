version: '3.8'

services:
  vocal-auto-web:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: vocal-auto-web-client
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - FLASK_DEBUG=0
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    volumes:
      # 挂载配置文件（可选，用于动态配置）
      - ./config:/app/config:ro
      # 挂载数据目录（用于持久化测试数据）
      - ./data:/app/data
      # 挂载报告目录（用于持久化测试报告）
      - ./reports:/app/reports
      # 挂载日志目录
      - vocal_auto_logs:/app/logs
    restart: unless-stopped
    networks:
      - vocal-auto-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 可选：添加Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: vocal-auto-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro  # SSL证书目录（如果需要HTTPS）
    depends_on:
      - vocal-auto-web
    restart: unless-stopped
    networks:
      - vocal-auto-network

volumes:
  vocal_auto_logs:
    driver: local

networks:
  vocal-auto-network:
    driver: bridge
