import pandas as pd
import sys

def fill_ids(input_file, output_file=None):
    """
    填充 Excel 文件中 realtime sheet 的 ID 列
    
    参数:
    input_file (str): 输入 Excel 文件路径
    output_file (str): 输出 Excel 文件路径，默认为 None（覆盖原文件）
    """
    try:
        # 读取 Excel 文件
        xls = pd.ExcelFile(input_file)
        
        # 获取指定工作表中的数据
        df = xls.parse('realtime')
        
        # 检查是否存在 turns 列
        if 'turns' not in df.columns:
            print("错误：工作表中不存在 'turns' 列。")
            return
        
        # 初始化 ID
        current_id = 0
        id_list = []
        
        # 根据 turns 列的值生成 ID 列表
        for value in df['turns']:
            if value == '闲聊1':
                current_id += 1
                id_list.append(current_id)
            else:
                id_list.append(current_id)
        
        # 将生成的 ID 列表添加到数据框
        df['ID'] = id_list
        
        # 如果未指定输出文件，则覆盖原文件
        if output_file is None:
            output_file = input_file
        
        # 写入 Excel 文件
        with pd.ExcelWriter(output_file, engine='openpyxl', mode='w') as writer:
            df.to_excel(writer, sheet_name='realtime', index=False)
            
        print(f"成功填充 ID 列并保存到 {output_file}")
        
    except Exception as e:
        print(f"发生错误: {e}")

if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) < 2:
        print("用法: python excel_id_filler.py <输入文件> [输出文件]")
        sys.exit(1)
    
    input_file = sys.argv[1]
    
    # 如果提供了第二个参数，则作为输出文件
    if len(sys.argv) > 2:
        output_file = sys.argv[2]
        fill_ids(input_file, output_file)
    else:
        fill_ids(input_file)
