pipeline {
    agent {
        label 'vocal_server'
    }

    parameters {
        // Define parameters corresponding to script arguments
        string(name: 'TARGET', description: '测试目录或文件（路径基于BASE_INPUT），例如：情感觉醒/全车情感觉醒/')
        choice(name: 'AUDIO_FORMAT', choices: ['pcm', 'ogg'], description: '输入音频文件的格式')
        booleanParam(name: 'CONTINUE_TESTING', defaultValue: false, description: '继续执行失败或未执行的用例')
        string(name: 'BASE_INPUT', defaultValue: '/media/mach/0209/MC-ASA/端到端语音/测试bmk', description: '测试用例基础输入路径')
        string(name: 'BASE_OUTPUT', defaultValue: '/media/mach/0209/MC-ASA/端到端语音/bmk测试报告', description: '测试报告基础输出路径')
        booleanParam(name: 'SEND_REPORT', defaultValue: true, description: '执行完成后发送飞书报告')
        string(name: 'CONCURRENCY', defaultValue: '1', description: '最大并发执行文件数量 (默认为1)')
        // Add other parameters like CONFIG, MAX_HISTORY if needed
    }

    environment {
        // Define environment variables if needed, e.g., for secrets or paths
        // Example: API_TOKEN = credentials('my-api-token-credential-id')
        PYTHON_EXEC = 'python3' // Adjust if your agent uses a different command (e.g., 'python')
    }

    stages {
        stage('Setup Python Environment') {
            steps {
                echo 'Setting up Python environment...'
                // Check if Python exists
                sh "${env.PYTHON_EXEC} --version"
                // Install requirements directly
                sh """
                    echo "Installing dependencies from requirements.txt..."
                    pip3 install --upgrade pip
                    pip3 install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
                """
            }
        }

        stage('Run Vocal Tests') {
            steps {
                // Wrap the execution step to ensure user variables are injected
                wrap([$class: 'BuildUser']) {
                    script {
                        echo "Current BUILD_USER_ID: ${env.BUILD_USER_ID}" // Check if it's available now
                        echo 'Constructing test command within BuildUser wrap...'
                        // Base command
                        def command = "${env.PYTHON_EXEC} run_vocal_tests.py --base-input '${params.BASE_INPUT}' --base-output '${params.BASE_OUTPUT}' --target '${params.TARGET}' --audio-format '${params.AUDIO_FORMAT}'"

                        // Add optional username using Jenkins environment variable (now expected to be set by wrap)
                        if (env.BUILD_USER_ID && env.BUILD_USER_ID.trim()) {
                            echo "Using Jenkins User ID from wrap for username: ${env.BUILD_USER_ID}"
                            command += " --username '${env.BUILD_USER_ID}'"
                        } else {
                            echo "Jenkins BUILD_USER_ID still not found or empty even within wrap, running without specific username."
                        }

                        // Add optional continue flag
                        if (params.CONTINUE_TESTING) {
                            command += " --continue"
                        }

                        // Add optional send report flag
                        if (params.SEND_REPORT) {
                            command += " --send-report"
                        }

                        // Add concurrency parameter
                        if (params.CONCURRENCY && params.CONCURRENCY.trim()) {
                            command += " --concurrency ${params.CONCURRENCY}"
                        }

                        echo "Executing: ${command}"
                        // Execute the command
                        sh command
                    }
                }
            }
        }
    }

    post {
        always {
            echo 'Pipeline finished. Archiving results...'
            // Archive the entire base output directory.
            // Note: This might capture more than just the current run's reports if not cleaned.
            // Adjust the path or use specific file patterns if needed.
            archiveArtifacts artifacts: "${params.BASE_OUTPUT}/**", allowEmptyArchive: true
        }
        success {
            echo 'Tests completed successfully!'
            // Add success notifications (e.g., Slack, Email)
        }
        failure {
            echo 'Tests failed.'
            // Add failure notifications
        }
    }
}