#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import argparse
import asyncio
import sys
from pathlib import Path
import time
from typing import Dict
from urllib.parse import quote # Import quote for URL encoding

# Import rich for table display
from rich.console import Console
from rich.table import Table

# Ensure the project root (parent directory of this script's location) is in the path
# This allows importing modules from the project (like run_realtime)
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

from run_realtime import MultiRealtimeRunner
from utils.send_report import send_feishu_bot_message


FEISHU_LINK_BASE_URL = "http://*************:8080/1/%E7%AB%AF%E5%88%B0%E7%AB%AF%E8%AF%AD%E9%9F%B3/%E6%B5%8B%E8%AF%95bmk/"

def display_test_summary(excel_files: list[Path], base_input_path: Path):
    """待执行测试用例概览"""
    if not excel_files:
        return

    console = Console()
    table = Table(title="\n--- 待执行测试用例概览 ---", show_header=True, header_style="bold magenta")
    table.add_column("序号", style="dim", width=6, justify="right")
    table.add_column("所属目录", style="dim", width=55)
    table.add_column("测试文件", style="cyan")

    grouped_files = {}
    file_index = 0 # 初始化文件序号计数器
    for file_path in excel_files:
        try:
            # 计算相对于基础路径的路径，用于分组
            relative_path = file_path.relative_to(base_input_path)
            parent_dir_rel = relative_path.parent
        except ValueError:
             # 如果文件不在 base_input 下，使用绝对路径的父目录作为键
             parent_dir_rel = file_path.parent # Fallback

        parent_key = str(parent_dir_rel) # Use string representation as dict key
        if parent_key not in grouped_files:
            grouped_files[parent_key] = []
        grouped_files[parent_key].append(file_path.name)

    # 打印分组后的文件列表到表格
    first_dir = True
    for dir_key, files in sorted(grouped_files.items()):
        # 如果 key 是 '.' 表示在 base_input_path 根目录
        display_dir = dir_key if dir_key != '.' else f"{base_input_path.name}/ (根目录)"
        
        for filename in sorted(files): # Sort files within directory
            file_index += 1 # 递增序号
            table.add_row(str(file_index), display_dir, filename)

                
    console.print(table)
    print("---------------------------")

def display_final_summary(execution_details: list, aggregated_counts: Dict[str, int]):
    """显示详细和总计的批量执行结果统计"""
    console = Console()

    # --- 详细结果表格 ---
    detail_table = Table(title="\n批量执行详细结果", show_header=True, header_style="bold cyan")
    detail_table.add_column("序号", style="dim", width=6, justify="right")
    detail_table.add_column("测试文件", style="white", min_width=30, max_width=50)
    detail_table.add_column("文件状态", style="", width=10)
    detail_table.add_column("耗时(秒)", style="magenta", width=10, justify="right")
    # Add columns for case counts
    detail_table.add_column("用例通过", style="green", width=10, justify="right")
    detail_table.add_column("用例失败", style="red", width=10, justify="right")
    detail_table.add_column("用例跳过", style="yellow", width=10, justify="right")
    detail_table.add_column("错误信息", style="red", no_wrap=False) # Allow wrapping for errors

    total_files = len(execution_details)
    successful_files = 0
    failed_files = 0

    for i, result in enumerate(execution_details):
        file_status = result['status']
        status_style = "green" if file_status == 'Success' else "red"
        error_msg = result.get('error', '') # Get error message if it exists
        duration_str = f"{result['duration']:.2f}" if result['duration'] is not None else "N/A"
        
        # Get case counts from the stored summary, handle potential missing summary
        summary = result.get('summary')
        passed_count_str = "N/A"
        failed_count_str = "N/A"
        skipped_count_str = "N/A"

        if summary and not summary.get('error'):
             passed_count_str = str(summary.get('passed', 0))
             failed_val = summary.get('failed', 0)
             # Handle specific internal error case
             if failed_val == -1:
                 failed_count_str = "[red]运行错误[/]"
             else:
                 failed_count_str = str(failed_val)
             skipped_count_str = str(summary.get('skipped', 0))
        elif summary and summary.get('error'): # Handle case where runner explicitly returned an error
            failed_count_str = "[red]运行错误[/]"
            # passed/skipped remain N/A

        detail_table.add_row(
            str(i + 1),
            result['file'],
            f"[{status_style}]{file_status}[/]", # Apply style here for file status
            duration_str,
            # Case counts (already strings)
            passed_count_str,
            failed_count_str, # String might contain style
            skipped_count_str,
            error_msg
        )
        
        if file_status == 'Success':
            successful_files += 1
        else:
            failed_files += 1
            
    console.print(detail_table)

    # --- 总计结果表格 ---
    summary_table = Table(title="批量执行结果总计", show_header=True, header_style="bold blue")
    summary_table.add_column("统计项", style="dim")
    summary_table.add_column("数量", style="bold", justify="right")

    # Calculate total processed cases
    total_processed_cases = aggregated_counts.get('passed', 0) + aggregated_counts.get('failed', 0) + aggregated_counts.get('skipped', 0)

    summary_table.add_row("处理文件总数", str(total_files))
    summary_table.add_row("成功文件数", str(successful_files), style="green" if successful_files == total_files and total_files > 0 else None)
    summary_table.add_row("失败文件数", str(failed_files), style="red" if failed_files > 0 else None)
    summary_table.add_row("-", "-") # Separator
    summary_table.add_row("[bold]总计处理用例数[/]", str(total_processed_cases)) # Add total processed case IDs
    summary_table.add_row("[bold]总计测试用例通过数[/]", str(aggregated_counts.get('passed', 0)), style="green")
    summary_table.add_row("[bold]总计测试用例失败数[/]", str(aggregated_counts.get('failed', 0)), style="red" if aggregated_counts.get('failed', 0) > 0 else None)
    summary_table.add_row("[bold]总计测试用例跳过数[/]", str(aggregated_counts.get('skipped', 0)), style="yellow" if aggregated_counts.get('skipped', 0) > 0 else None)
    
    console.print(summary_table)

async def process_single_excel_file(excel_file_path: Path, base_input_path: Path, base_output_path: Path, args: argparse.Namespace, file_index: int, total_files: int):
    """异步处理单个Excel测试文件"""
    print(f"--- 开始处理文件 {file_index}/{total_files}: {excel_file_path.name} ---")
    start_run_time = time.time()
    
    relative_path_str = str(excel_file_path.name) # Fallback to filename if relative path fails
    status = "Failed"  # Default status
    duration = None
    error_info = ""
    run_summary_details = None # Summary from MultiRealtimeRunner
    task_for_report = None   # Data for Feishu report

    try:
        # --- 计算相关路径 ---
        username_part = args.username if args.username and args.username.strip() else "default"
        output_dir_sublevel = f"测试报告_{username_part}"

        try:
            relative_path = excel_file_path.relative_to(base_input_path)
            relative_path_str = str(relative_path)
        except ValueError:
            error_info = f"无法计算相对路径，文件 {excel_file_path} 可能不在基础输入路径 {base_input_path} 下。"
            print(f"警告: {error_info} 文件将被标记为跳过。")
            status = "Skipped" 
            # No further processing for this file, will be handled in finally
            return {
                'file': relative_path_str, 
                'status': status,
                'duration': time.time() - start_run_time,
                'error': error_info,
                'summary': None,
                'task_for_report': None
            }

        relative_dir = relative_path.parent
        output_dir = base_output_path / output_dir_sublevel / relative_dir
        output_dir.mkdir(parents=True, exist_ok=True)

        # Prepare paths for Feishu links
        excel_name_without_ext = excel_file_path.stem
        relative_report_path = Path(output_dir_sublevel) / relative_dir / f"{excel_name_without_ext}_testReport.xlsx"
        relative_audio_dir_path = Path(output_dir_sublevel) / relative_dir / excel_name_without_ext
        
        report_link_relative_encoded = quote(str(relative_report_path))
        audio_link_relative_encoded = quote(str(relative_audio_dir_path)) + '/'
        report_link = FEISHU_LINK_BASE_URL + report_link_relative_encoded
        audio_link = FEISHU_LINK_BASE_URL + audio_link_relative_encoded

        task_for_report = {
            'name': excel_file_path.name,
            'report_link': report_link,
            'audio_link': audio_link
        }

        # 确定音频输入目录
        audio_input_dir = excel_file_path.parent / args.audio_format
        audio_dir_for_runner = str(audio_input_dir)
        if not audio_input_dir.is_dir():
            print(f"  警告 (文件: {excel_file_path.name}): 未找到预期的音频输入目录: {audio_input_dir}")
            # Runner will likely fail or skip cases, this is an informational warning here.

        # --- 实例化并运行 Runner ---
        runner = MultiRealtimeRunner(
            excel_file=str(excel_file_path),
            audio_dir=audio_dir_for_runner,
            output_dir=str(output_dir),
            audio_format=args.audio_format,
            username=args.username,
            continue_testing=args.continue_testing
        )
        
        print(f"  开始执行 {excel_file_path.name} 的内部测试用例...")
        run_summary = await runner.run_tests() # This is the summary for test cases
        print(f"  完成执行 {excel_file_path.name} 的内部测试用例。")
        
        run_summary_details = run_summary # 将执行摘要保存到 run_summary_details

        # 根据 run_summary_details 的内容决定文件状态 status
        # status 在函数开始时已初始化为 "Failed"
        if run_summary_details:
            has_runner_error = run_summary_details.get('error') # 检查执行器是否报告了内部错误
            num_failed_cases = run_summary_details.get('failed', 0) # 获取失败用例数，若无则为0

            if has_runner_error:
                status = "Failed" # 执行器内部错误，文件状态为 Failed
                if not error_info: # 如果 broader_error_info 尚未被外部异常设置
                    error_info = f"测试执行器内部错误: {run_summary_details.get('error_message', '未提供详细错误信息')}"
            elif num_failed_cases > 0 or num_failed_cases == -1: # -1 是一个特定的执行器错误代码
                status = "Failed" # 有测试用例失败，文件状态为 Failed
                # 此处不需要填充 error_info，因为用例失败情况会在摘要中显示，不属于文件处理异常
            else:
                # 没有执行器错误，且失败用例数为0 (且不为-1)
                status = "Success" # 文件处理和用例执行均成功
        else:
            # run_summary_details 为 None，表明执行器未能返回有效的摘要信息
            status = "Failed" # 文件状态为 Failed
            if not error_info: # 如果 error_info 尚未被外部异常设置
                error_info = "测试执行器未返回有效的测试摘要"
        # 原来的 status = "Success" 已被此逻辑块取代

    except Exception as e:
        error_info = str(e)
        print(f"处理文件 {excel_file_path.name} 时发生严重错误: {e}")
        import traceback
        traceback.print_exc()
        status = "Failed" # 确保在任何异常情况下，文件状态都为 Failed
        # run_summary_details 会保留异常发生前的值 (如果已设置)，或为 None (如果异常发生在 run_tests 期间)
        # task_for_report 的处理逻辑类似

    finally:
        end_run_time = time.time()
        duration = end_run_time - start_run_time
        print(f"--- 完成处理文件 {excel_file_path.name}, 耗时: {duration:.2f}s, 状态: {status} ---")
        
        return {
            'file': relative_path_str,
            'status': status,
            'duration': duration,
            'error': error_info,
            'summary': run_summary_details, # Dict from runner.run_tests() or None
            'task_for_report': task_for_report # Dict for Feishu or None
        }

async def main():
    parser = argparse.ArgumentParser(description='批量执行 VocalAuto 测试用例')
    parser.add_argument(
        '--base-input', 
        type=str, 
        default='/media/mach/0209/MC-ASA/端到端语音/测试bmk', 
        help='包含测试用例和音频的基础输入路径'
    )
    parser.add_argument(
        '--base-output', 
        type=str, 
        default='/media/mach/0209/MC-ASA/端到端语音/bmk测试报告', 
        help='测试报告和输出音频的基础输出路径'
    )
    parser.add_argument(
        '--target', 
        type=str, 
        required=True, 
        help='相对于基础输入路径的测试目标 (单个 .xlsx 文件或目录)'
    )
    parser.add_argument(
        '--audio-format', 
        type=str, 
        default='ogg', 
        choices=['pcm', 'ogg'], 
        help='输入音频文件的格式 (pcm 或 ogg)，默认为 ogg'
    )
    # 可以添加 MultiRealtimeRunner 支持的其他参数，例如:
    # parser.add_argument('--max-history', '-m', type=int, help='历史对话最大保留轮数')
    # parser.add_argument('--config', '-c', type=str, help='配置文件路径')
    parser.add_argument('--username', '-u', type=str, help='用户名，用于生成 session-id (可选)')
    parser.add_argument('--continue', action='store_true', dest='continue_testing', help='继续执行失败或未执行的用例')
    parser.add_argument('--send-report', action='store_true', help='执行完成后发送飞书报告')
    parser.add_argument(
        '--concurrency',
        '-N',
        type=int,
        default=1, # 默认并发数为5
        help='最大并发执行文件数量 (默认为 1)'
    )


    args = parser.parse_args()

    base_input_path = Path(args.base_input).resolve()
    base_output_path = Path(args.base_output).resolve()
    target_arg = args.target # 用户提供的目标（文件或目录）

    # 确定目标的完整绝对路径
    target_path = base_input_path / target_arg

    print(f"基础输入路径: {base_input_path}")
    print(f"基础输出路径: {base_output_path}")
    print(f"目标参数: {target_arg}")
    print(f"解析后目标路径: {target_path}")
    print(f"音频格式: {args.audio_format}")
    print("-" * 60)

    excel_files_to_process = []

    if not base_input_path.is_dir():
        print(f"错误: 基础输入路径不存在或不是一个目录: {base_input_path}")
        sys.exit(1)
        
    if not target_path.exists():
        print(f"错误: 目标路径不存在: {target_path}")
        sys.exit(1)
    elif target_path.is_dir():
        print(f"在目录中搜索 Excel 文件: {target_path}")
        # 使用 rglob 进行递归搜索
        excel_files_to_process = sorted(list(target_path.rglob("*.xlsx")))
        if not excel_files_to_process:
            print(f"在 {target_path} 及其子目录中未找到 *.xlsx 文件")
    elif target_path.is_file() and target_path.suffix.lower() == '.xlsx':
        print(f"处理单个 Excel 文件: {target_path}")
        excel_files_to_process = [target_path]
    else:
        print(f"错误: 目标路径既不是有效目录也不是 .xlsx 文件: {target_path}")
        sys.exit(1)

    if not excel_files_to_process:
        print("没有找到需要处理的 Excel 文件。")
        sys.exit(0)
        
    print(f"找到 {len(excel_files_to_process)} 个 Excel 文件准备处理。")

    # --- 用例信息统计与展示 ---
    display_test_summary(excel_files_to_process, base_input_path)
    # --- 统计结束 ---

    total_files = len(excel_files_to_process)
    execution_details = [] # Store results for each file
    tasks_for_report = [] # Store details for Feishu report
    # Initialize aggregate counters for test cases
    total_passed_cases = 0
    total_failed_cases = 0
    total_skipped_cases = 0

    # --- 并发处理所有符合条件的 Excel 文件 ---
    if excel_files_to_process:
        processing_tasks = []
        # 初始化 Semaphore，基于用户指定的并发数
        semaphore = asyncio.Semaphore(args.concurrency)

        # 辅助函数，用于包装原始任务并使用 Semaphore 控制并发
        async def run_with_semaphore(task_coro):
            async with semaphore: # 在执行任务前获取信号量，任务结束后自动释放
                return await task_coro

        for i, excel_file_path in enumerate(excel_files_to_process):
            # 为每个文件创建一个原始的异步任务协程
            single_file_task_coro = process_single_excel_file(
                excel_file_path, 
                base_input_path, 
                base_output_path, 
                args, 
                i + 1,  # 当前文件序号 (1-based)
                total_files # 文件总数
            )
            # 将包装后的任务添加到列表
            processing_tasks.append(run_with_semaphore(single_file_task_coro))

        print(f">>> 开始并发处理 {len(processing_tasks)} 个测试文件 (最大并发数: {args.concurrency})... <<<")
        # asyncio.gather 会按顺序返回结果 (如果 return_exceptions=False 且无异常)
        # 或者返回异常对象 (如果 return_exceptions=True)
        all_results = await asyncio.gather(*processing_tasks, return_exceptions=True)
        print(f">>> 所有 {len(all_results)} 个任务已完成处理。开始分析结果... <<<")

        # 重置/确保列表和计数器是干净的，然后从结果中填充
        execution_details = []
        tasks_for_report = []
        total_passed_cases = 0
        total_failed_cases = 0
        total_skipped_cases = 0

        for i, result_item in enumerate(all_results):
            print(f"分析结果 {i+1}/{len(all_results)}...")
            if isinstance(result_item, Exception):
                # 这种情况通常意味着 process_single_excel_file 内部发生了未被其自身 try-except 捕获的严重错误
                # 或者 asyncio.gather 本身遇到的问题。
                # process_single_excel_file 被设计为总是返回一个字典，所以这应该很少见。
                print(f"  严重系统错误：一个并发任务直接抛出异常: {result_item}")
                execution_details.append({
                    'file': f"未知文件 (任务 {i+1} 发生严重异常)",
                    'status': "Error", 
                    'duration': 0, # 未知时长
                    'error': str(result_item),
                    'summary': None
                })
            elif result_item: # 确认 result_item 是 process_single_excel_file 返回的字典
                print(f"  文件: {result_item.get('file', 'N/A')}, 状态: {result_item.get('status', 'N/A')}")
                execution_details.append({
                    'file': result_item['file'],
                    'status': result_item['status'],
                    'duration': result_item['duration'],
                    'error': result_item['error'],
                    'summary': result_item['summary']
                })
                
                if result_item.get('task_for_report'):
                    tasks_for_report.append(result_item['task_for_report'])
                
                # 从每个文件的 summary 中聚合用例统计数据
                summary = result_item.get('summary')
                if summary and not summary.get('error'): # 仅当 summary 存在且没有内部错误标记时才计数
                    total_passed_cases += summary.get('passed', 0)
                    total_failed_cases += summary.get('failed', 0)
                    total_skipped_cases += summary.get('skipped', 0)
                elif result_item['status'] == "Skipped": 
                    # 文件本身被跳过 (例如路径问题), 不计入用例统计
                    print(f"    文件 {result_item.get('file', 'N/A')} 被跳过，不计入用例统计。")
                elif result_item['status'] == "Failed" and not summary :
                    # 文件处理失败，且没有runner的summary (例如，runner实例化前就出错了)
                    # 这种情况下，测试用例数可以认为都是0，不增加任何计数
                    print(f"    文件 {result_item.get('file', 'N/A')} 处理失败，无用例执行统计。")


            else: # result_item is None or not an Exception (should not happen with return_exceptions=True and proper task returns)
                print(f"  警告: 收到一个空的或无效的结果项: {result_item}")
                execution_details.append({
                    'file': f"未知文件 (任务 {i+1} 返回无效结果)",
                    'status': "Error",
                    'duration': 0,
                    'error': "并发任务返回了无效数据",
                    'summary': None
                })
    else: # excel_files_to_process is empty
        print("没有找到需要处理的 Excel 文件。")
        # execution_details, tasks_for_report, and case counters remain empty/zero

    print("=" * 60)
    print("所有文件的批量处理已完成。")

    # --- 显示最终统计结果 ---
    aggregated_results = {
        'passed': total_passed_cases,
        'failed': total_failed_cases,
        'skipped': total_skipped_cases
    }
    display_final_summary(execution_details, aggregated_results)

    # --- 发送飞书报告 (如果指定了 --send-report) --- 
    if args.send_report:
        print("\n准备发送飞书报告...")
        # Determine the user name for the report (use Jenkins ID or default)
        report_username = args.username if args.username and args.username.strip() else "default"
        # Calculate total processed cases
        total_processed_cases = total_passed_cases + total_failed_cases + total_skipped_cases
        
        report_data = {
            "user_name": report_username, # Pass the username (send_report maps it if possible)
            "total": total_processed_cases,
            "success": total_passed_cases,
            "fail": total_failed_cases,
            "skip": total_skipped_cases,
            "tasks": tasks_for_report # Pass the collected task details
        }
        
        try:
            send_feishu_bot_message(report_data)
            print("飞书报告发送尝试完成。")
        except Exception as report_err:
            print(f"发送飞书报告时出错: {report_err}")


if __name__ == "__main__":
    # 确保在 Windows 上使用正确的事件循环策略（如果需要）
    # if sys.platform == "win32":
    #     asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(main()) 