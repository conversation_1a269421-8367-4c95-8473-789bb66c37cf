name: vocalauto

runtime: python

env:
  - name: TZ
    value: Asia/Shanghai

web:
  - name: vocalauto
    cmd: python3 webapp/app.py
    port:
      - 80:5000
    external_port:
      - 80:5000
    liveness_probe:
      initial_delay_seconds: 10
      period_seconds: 10
      timeout_seconds: 10
      failure_threshold: 6
      exec:
        cmd: echo ok

    readiness_probe:
      initial_delay_seconds: 10
      period_seconds: 10
      timeout_seconds: 10
      failure_threshold: 6
      exec:
        cmd: echo ok
