#!/bin/bash

# 参数设置（根据实际情况调整）
SAMPLE_RATE=44100
FORMAT="s16le"

for pcm_file in *.pcm; do
    if [ -f "$pcm_file" ]; then
        ogg_file="${pcm_file%.pcm}.ogg"
        echo "正在转换: $pcm_file → $ogg_file"

        # 先尝试立体声
        ffmpeg -f "$FORMAT" -ar "$SAMPLE_RATE" -ac 2 -i "$pcm_file" -c:a libvorbis "$ogg_file" -hide_banner -loglevel error

        if [ $? -ne 0 ]; then
            echo "检测到错误，尝试单声道..."
            # 失败后尝试单声道
            ffmpeg -f "$FORMAT" -ar "$SAMPLE_RATE" -ac 1 -i "$pcm_file" -c:a libvorbis "$ogg_file" -hide_banner -loglevel error
            if [ $? -eq 0 ]; then
                echo "✓ 转换成功（单声道）: $ogg_file"
            else
                echo "✗ 转换失败: $pcm_file（请检查格式参数）"
            fi
        else
            echo "✓ 转换成功（立体声）: $ogg_file"
        fi
    fi
done
