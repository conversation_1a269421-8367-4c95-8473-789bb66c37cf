import wave
import subprocess
import os
from concurrent.futures import ThreadPoolExecutor

class AudioConverter:
    def __init__(self, input_directory):
        self.input_directory = input_directory
        self.ogg_directory = os.path.join(os.path.dirname(input_directory), 'ogg')
        self.pcm_directory = os.path.join(os.path.dirname(input_directory), 'pcm')

    def find_wav_files(self):
        """
        查找指定目录下的所有.wav文件。
        :return: 包含所有.wav文件路径的列表
        """
        wav_files = []
        for root, dirs, files in os.walk(self.input_directory):
            for file in files:
                if file.lower().endswith('.wav'):
                    wav_files.append(os.path.join(root, file))
        return wav_files

    def wav_to_ogg(self, input_wav_path):
        """
        将WAV文件转换为OGG文件，OGG文件与WAV文件同名，仅后缀为.ogg。

        :param input_wav_path: 输入的WAV文件路径
        """
        base_name = os.path.splitext(os.path.basename(input_wav_path))[0]
        output_ogg_path = os.path.join(self.ogg_directory, base_name + '.ogg')
        os.makedirs(self.ogg_directory, exist_ok=True)
        try:
            subprocess.run(['ffmpeg', '-y', '-i', input_wav_path, output_ogg_path], check=True)
            print(f"成功将 {input_wav_path} 转换为 {output_ogg_path}")
        except subprocess.CalledProcessError as e:
            print(f"转换失败: {e}")

    def wav_to_pcm(self, input_wav_path):
        """
        将WAV文件转换为PCM文件，PCM文件与WAV文件同名，仅后缀为.pcm。

        :param input_wav_path: 输入的WAV文件路径
        :return: 输出的PCM文件路径
        """
        base_name = os.path.splitext(os.path.basename(input_wav_path))[0]
        output_pcm_path = os.path.join(self.pcm_directory, base_name + '.pcm')
        os.makedirs(self.pcm_directory, exist_ok=True)
        try:
            with wave.open(input_wav_path, 'rb') as wf:
                pcm_data = wf.readframes(wf.getnframes())
            with open(output_pcm_path, 'wb') as pf:
                pf.write(pcm_data)
            print(f"成功将 {input_wav_path} 转换为 {output_pcm_path}")
            return output_pcm_path
        except Exception as e:
            print(f"转换失败: {e}")
            return None

    def convert_file(self, wav_file):
        """
        转换单个WAV文件为.ogg和.pcm文件。
        """
        self.wav_to_ogg(wav_file)
        self.wav_to_pcm(wav_file)
        print(f"已处理文件: {wav_file}")

    def convert_all_wav_files(self):
        """
        并发转换指定目录下的所有.wav文件为.ogg和.pcm文件。
        """
        wav_files = self.find_wav_files()
        with ThreadPoolExecutor() as executor:
            executor.map(self.convert_file, wav_files)

if __name__ == "__main__":
    # 输入wav文件路径
    input_wav = r'C:\Megvii VocalAuto\vocalauto\data\realtime\web闲聊\111'
    converter = AudioConverter(input_wav)
    converter.convert_all_wav_files()