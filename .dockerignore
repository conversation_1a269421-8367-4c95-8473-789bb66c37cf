# Git相关
.git
.gitignore
.gitattributes

# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE相关
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统相关
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log
logs/

# 测试相关
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# 数据文件（可选择性忽略）
data/
reports/
dataset_*/

# 备份文件
backup/
*.bak
*.backup

# 临时文件
tmp/
temp/
*.tmp

# Docker相关
Dockerfile*
docker-compose*.yml
.dockerignore

# 部署相关
deploy.sh
nginx.conf
.env
.env.*

# 文档
README.md
*.md
docs/

# 其他
.editorconfig
Jenkinsfile
