// Vocal Auto Web Client JavaScript

class VocalAutoClient {
    constructor() {
        this.socket = null;
        this.sessionId = null;
        this.isRecording = false;
        this.mediaRecorder = null;
        this.audioChunks = [];
        this.conversationHistory = [];
        this.realtimeInitialized = false;
        this.ttsInitialized = false;
        
        this.init();
    }
    
    init() {
        this.initSocket();
        this.bindEvents();
        this.log('系统初始化完成');
    }
    
    initSocket() {
        this.socket = io();
        
        this.socket.on('connect', () => {
            this.updateConnectionStatus('connected', '已连接');
            this.log('WebSocket连接成功');
        });
        
        this.socket.on('disconnect', () => {
            this.updateConnectionStatus('disconnected', '连接断开');
            this.log('WebSocket连接断开');
        });
        
        this.socket.on('connected', (data) => {
            this.sessionId = data.session_id;
            this.log(`会话ID: ${this.sessionId}`);
        });
        
        // 实时对话事件
        this.socket.on('realtime_configured', (data) => {
            this.realtimeConfigured = data.success;
            if (data.success) {
                this.log('实时对话配置成功，可以开始对话');
                this.enableRealtimeControls();
            } else {
                this.log('实时对话配置失败', 'error');
            }
        });
        
        this.socket.on('realtime_text_delta', (data) => {
            this.appendToRealtimeChat(data.text, 'assistant', true);
        });
        
        this.socket.on('realtime_audio_delta', (data) => {
            // 播放音频片段
            this.playAudioChunk(data.audio);
        });
        
        // 音频对话完成事件
        this.socket.on('audio_conversation_complete', (data) => {
            if (data.success) {
                this.log(`音频对话完成 - Session: ${data.result.session_id}`, 'success');
                this.log(`会话URL: ${data.result.session_url}`);

                // 添加助手回复到对话历史
                if (data.result.text) {
                    this.conversationHistory.push({
                        role: 'assistant',
                        type: 'text',
                        text: data.result.text
                    });
                    this.appendToRealtimeChat(data.result.text, 'assistant');
                }
            } else {
                this.log(`音频对话失败: ${data.error}`, 'error');
            }
        });

        // 文本对话完成事件
        this.socket.on('text_conversation_complete', (data) => {
            if (data.success) {
                this.log(`文本对话完成 - Session: ${data.result.session_id}`, 'success');
                this.log(`会话URL: ${data.result.session_url}`);

                // 添加助手回复到对话历史
                if (data.result.text) {
                    this.conversationHistory.push({
                        role: 'assistant',
                        type: 'text',
                        text: data.result.text
                    });
                    this.appendToRealtimeChat(data.result.text, 'assistant');
                }
            } else {
                this.log(`文本对话失败: ${data.error}`, 'error');
            }
        });

        // TTS事件
        this.socket.on('tts_initialized', (data) => {
            this.ttsInitialized = data.success;
            if (data.success) {
                this.log('TTS初始化成功');
                this.enableTTSControls();
            } else {
                this.log('TTS初始化失败', 'error');
            }
        });

        this.socket.on('tts_audio_complete', (data) => {
            this.displayTTSAudio(data.audio);
            this.log('TTS语音生成完成');
        });

        this.socket.on('tts_complete', (data) => {
            this.log('TTS处理完成');
        });

        this.socket.on('error', (data) => {
            this.log(`错误: ${data.message}`, 'error');
        });
    }
    
    bindEvents() {
        // 实时对话事件
        document.getElementById('setup-realtime-btn').addEventListener('click', () => {
            this.setupRealtime();
        });

        document.getElementById('reset-realtime-config-btn').addEventListener('click', () => {
            this.resetRealtimeConfig();
        });

        document.getElementById('record-btn').addEventListener('click', () => {
            this.toggleRecording();
        });

        document.getElementById('send-realtime-btn').addEventListener('click', () => {
            this.sendRealtimeMessage();
        });

        document.getElementById('transcript-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendRealtimeMessage();
            }
        });
        
        // TTS事件
        document.getElementById('init-tts-btn').addEventListener('click', () => {
            this.initTTS();
        });

        document.getElementById('reset-tts-config-btn').addEventListener('click', () => {
            this.resetTTSConfig();
        });

        document.getElementById('send-tts-btn').addEventListener('click', () => {
            this.sendTTSText();
        });

        document.getElementById('clear-tts-btn').addEventListener('click', () => {
            document.getElementById('tts-text').value = '';
            document.getElementById('tts-audio-container').innerHTML = '';
        });
        
        // 系统日志
        document.getElementById('clear-log-btn').addEventListener('click', () => {
            document.getElementById('system-log').innerHTML = '';
        });
    }
    
    updateConnectionStatus(status, text) {
        const indicator = document.querySelector('.status-indicator');
        const statusText = document.getElementById('status-text');
        
        indicator.className = `status-indicator status-${status}`;
        statusText.textContent = text;
    }
    
    log(message, type = 'info') {
        const logContainer = document.getElementById('system-log');
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        
        let className = 'text-info';
        if (type === 'error') className = 'text-danger';
        else if (type === 'success') className = 'text-success';
        else if (type === 'warning') className = 'text-warning';
        
        logEntry.className = className;
        logEntry.innerHTML = `[${timestamp}] ${message}`;
        
        logContainer.appendChild(logEntry);
        logContainer.scrollTop = logContainer.scrollHeight;
    }
    
    // 实时对话相关方法
    setupRealtime() {
        const envConfig = document.getElementById('env-config').value;
        const roleId = document.getElementById('realtime-role-id').value;
        const pattern = document.getElementById('realtime-pattern').value;
        const volume = parseFloat(document.getElementById('realtime-volume').value);
        const speed = parseFloat(document.getElementById('realtime-speed').value);
        const outputLanguage = document.getElementById('output-language').value;

        this.log(`保存实时对话配置 - 环境: ${envConfig}, 角色: ${roleId}, 模式: ${pattern}, 音量: ${volume}, 语速: ${speed}, 语言: ${outputLanguage || '普通话'}`);

        this.socket.emit('setup_realtime', {
            env_config: envConfig,
            role_id: roleId,
            pattern: pattern,
            volume_ratio: volume,
            speed_ratio: speed,
            output_language: outputLanguage
        });
    }

    resetRealtimeConfig() {
        document.getElementById('env-config').value = 'test';
        document.getElementById('realtime-role-id').value = 'yinhejingling';
        document.getElementById('realtime-pattern').value = 'normal';
        document.getElementById('realtime-volume').value = '1.0';
        document.getElementById('realtime-speed').value = '1.0';
        document.getElementById('output-language').value = '';
        this.log('实时对话配置已重置为默认值');
    }
    
    enableRealtimeControls() {
        document.getElementById('record-btn').disabled = false;
        document.getElementById('transcript-input').disabled = false;
        document.getElementById('send-realtime-btn').disabled = false;
    }
    
    async toggleRecording() {
        if (!this.isRecording) {
            await this.startRecording();
        } else {
            this.stopRecording();
        }
    }
    
    async startRecording() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: 16000,  // 设置采样率为16k
                    channelCount: 1,    // 单声道
                    echoCancellation: true,
                    noiseSuppression: true
                }
            });

            // 尝试使用支持的音频格式
            let options = {};
            if (MediaRecorder.isTypeSupported('audio/webm;codecs=opus')) {
                options.mimeType = 'audio/webm;codecs=opus';
            } else if (MediaRecorder.isTypeSupported('audio/webm')) {
                options.mimeType = 'audio/webm';
            } else if (MediaRecorder.isTypeSupported('audio/mp4')) {
                options.mimeType = 'audio/mp4';
            }

            this.mediaRecorder = new MediaRecorder(stream, options);
            this.audioChunks = [];

            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.audioChunks.push(event.data);
                }
            };

            this.mediaRecorder.onstop = () => {
                this.processRecording();
            };

            this.mediaRecorder.start();
            this.isRecording = true;

            const recordBtn = document.getElementById('record-btn');
            recordBtn.classList.add('recording');
            recordBtn.innerHTML = '<i class="fas fa-stop"></i>';

            this.log(`开始录音 (格式: ${options.mimeType || 'default'})`);

        } catch (error) {
            this.log(`录音失败: ${error.message}`, 'error');
        }
    }
    
    stopRecording() {
        if (this.mediaRecorder && this.isRecording) {
            this.mediaRecorder.stop();
            this.isRecording = false;
            
            const recordBtn = document.getElementById('record-btn');
            recordBtn.classList.remove('recording');
            recordBtn.innerHTML = '<i class="fas fa-microphone"></i>';
            
            this.log('停止录音');
        }
    }
    
    async processRecording() {
        try {
            // 创建音频blob，保持原始格式
            const audioBlob = new Blob(this.audioChunks, {
                type: this.mediaRecorder.mimeType || 'audio/webm'
            });

            const arrayBuffer = await audioBlob.arrayBuffer();
            const base64Audio = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));

            this.log(`音频录制完成，大小: ${audioBlob.size} bytes, 格式: ${audioBlob.type}`);

            // 检查是否已配置
            if (!this.realtimeConfigured) {
                this.log('请先设置实时对话配置', 'warning');
                return;
            }

            // 发送音频到服务器进行完整的对话处理
            this.socket.emit('send_audio', {
                audio: base64Audio,
                transcript: document.getElementById('transcript-input').value || '',
                conversation_history: this.conversationHistory
            });

            this.log('音频已发送，开始新的对话会话...');

        } catch (error) {
            this.log(`音频处理失败: ${error.message}`, 'error');
        }
    }
    
    sendRealtimeMessage() {
        const transcript = document.getElementById('transcript-input').value.trim();
        if (!transcript) {
            this.log('请输入文本或录制音频', 'warning');
            return;
        }

        // 检查是否已配置
        if (!this.realtimeConfigured) {
            this.log('请先设置实时对话配置', 'warning');
            return;
        }

        // 添加用户消息到对话历史
        this.conversationHistory.push({
            role: 'user',
            type: 'text',
            text: transcript
        });

        this.appendToRealtimeChat(transcript, 'user');

        // 发送文本到服务器进行完整的对话处理
        this.socket.emit('send_text', {
            text: transcript,
            conversation_history: this.conversationHistory
        });

        document.getElementById('transcript-input').value = '';
        this.log(`发送文本消息，开始新的对话会话: ${transcript}`);
    }
    
    appendToRealtimeChat(text, role, isIncremental = false) {
        const chatContainer = document.getElementById('realtime-chat');
        
        if (role === 'user') {
            // 清空欢迎消息
            if (chatContainer.querySelector('.text-center')) {
                chatContainer.innerHTML = '';
            }
            
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message user-message';
            messageDiv.innerHTML = `<strong>用户:</strong> ${text}`;
            chatContainer.appendChild(messageDiv);
        } else if (role === 'assistant') {
            let messageDiv = chatContainer.querySelector('.message.assistant-message:last-child');
            
            if (!messageDiv || !isIncremental) {
                messageDiv = document.createElement('div');
                messageDiv.className = 'message assistant-message';
                messageDiv.innerHTML = '<strong>助手:</strong> ';
                chatContainer.appendChild(messageDiv);
            }
            
            if (isIncremental) {
                messageDiv.innerHTML += text;
            } else {
                messageDiv.innerHTML = `<strong>助手:</strong> ${text}`;
            }
        }
        
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }
    
    playAudioChunk(base64Audio) {
        try {
            const audioData = atob(base64Audio);
            const arrayBuffer = new ArrayBuffer(audioData.length);
            const view = new Uint8Array(arrayBuffer);
            
            for (let i = 0; i < audioData.length; i++) {
                view[i] = audioData.charCodeAt(i);
            }
            
            const audioBlob = new Blob([arrayBuffer], { type: 'audio/wav' });
            const audioUrl = URL.createObjectURL(audioBlob);
            
            const audio = new Audio(audioUrl);
            audio.play().catch(error => {
                this.log(`音频播放失败: ${error.message}`, 'error');
            });
            
        } catch (error) {
            this.log(`音频解码失败: ${error.message}`, 'error');
        }
    }
    
    // TTS相关方法
    initTTS() {
        const envConfig = document.getElementById('tts-env-config').value;
        const voiceId = document.getElementById('tts-voice-id').value;
        const speedRatio = parseFloat(document.getElementById('tts-speed').value);
        const volumeRatio = parseFloat(document.getElementById('tts-volume').value);
        const emotion = document.getElementById('tts-emotion').value;
        const language = document.getElementById('tts-language').value;

        this.log(`初始化TTS - 环境: ${envConfig}, 音色: ${voiceId}, 语速: ${speedRatio}, 音量: ${volumeRatio}, 情感: ${emotion || '默认'}, 语言: ${language || '普通话'}`);

        this.socket.emit('init_tts', {
            env_config: envConfig,
            voice_id: voiceId,
            speed_ratio: speedRatio,
            volume_ratio: volumeRatio,
            emotion: emotion,
            language: language
        });
    }

    resetTTSConfig() {
        document.getElementById('tts-env-config').value = 'test';
        document.getElementById('tts-voice-id').value = 'yinhejingling';
        document.getElementById('tts-speed').value = '1.0';
        document.getElementById('tts-volume').value = '1.0';
        document.getElementById('tts-emotion').value = '';
        document.getElementById('tts-language').value = '';
        this.log('TTS配置已重置为默认值');
    }
    
    enableTTSControls() {
        document.getElementById('tts-text').disabled = false;
        document.getElementById('send-tts-btn').disabled = false;
    }
    
    sendTTSText() {
        const text = document.getElementById('tts-text').value.trim();
        if (!text) {
            this.log('请输入要转换的文本', 'warning');
            return;
        }
        
        this.log(`发送TTS文本: ${text.substring(0, 50)}${text.length > 50 ? '...' : ''}`);
        
        this.socket.emit('send_tts_text', {
            text: text
        });
    }
    
    displayTTSAudio(base64Audio) {
        try {
            const audioData = atob(base64Audio);
            const arrayBuffer = new ArrayBuffer(audioData.length);
            const view = new Uint8Array(arrayBuffer);
            
            for (let i = 0; i < audioData.length; i++) {
                view[i] = audioData.charCodeAt(i);
            }
            
            const audioBlob = new Blob([arrayBuffer], { type: 'audio/mp3' });
            const audioUrl = URL.createObjectURL(audioBlob);
            
            const audioContainer = document.getElementById('tts-audio-container');
            const audioElement = document.createElement('audio');
            audioElement.controls = true;
            audioElement.src = audioUrl;
            audioElement.className = 'w-100 mt-2';
            
            // 清空之前的音频
            audioContainer.innerHTML = '';
            audioContainer.appendChild(audioElement);
            
            // 自动播放
            audioElement.play().catch(error => {
                this.log(`音频播放失败: ${error.message}`, 'error');
            });
            
        } catch (error) {
            this.log(`TTS音频处理失败: ${error.message}`, 'error');
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.vocalAutoClient = new VocalAutoClient();
});
