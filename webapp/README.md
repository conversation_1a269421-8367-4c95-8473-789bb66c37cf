# Vocal Auto Web Client

基于现有的 `workflow/realtime_api.py` 和 `workflow/tts_api.py` WebSocket实现的Web客户端，提供语音对话和文本转语音功能。

## 功能特性

### 1. 语音对话 (Realtime)
- **语音输入**: 支持浏览器录音，实时语音识别
- **文本输入**: 支持直接文本输入进行对话
- **实时响应**: 流式接收AI回复的文本和语音
- **对话历史**: 自动维护对话上下文

### 2. 文本转语音 (TTS)
- **文本输入**: 支持长文本输入
- **语音合成**: 实时生成高质量语音
- **参数调节**: 支持语速、音量调节
- **音色选择**: 支持不同音色选择

### 3. Web界面特性
- **响应式设计**: 支持桌面和移动设备
- **实时状态**: 显示连接状态和处理进度
- **音频控制**: 内置音频播放器和录音控制
- **系统日志**: 实时显示系统运行日志

## 技术架构

### 后端
- **Flask**: Web框架
- **Flask-SocketIO**: WebSocket通信
- **异步处理**: 支持并发连接和异步API调用
- **代理模式**: 封装现有的realtime_api和tts_api

### 前端
- **Bootstrap 5**: UI框架
- **Socket.IO**: 实时通信
- **Web Audio API**: 音频录制和播放
- **原生JavaScript**: 无额外框架依赖

## 安装和运行

### 1. 环境要求
- Python 3.8+
- 现代浏览器（支持WebRTC和Web Audio API）
- 麦克风权限（用于语音录制）

### 2. 安装依赖
```bash
cd webapp
pip install -r requirements.txt
```

### 3. 启动服务
```bash
# 使用启动脚本
chmod +x start.sh
./start.sh

# 或直接运行
python3 app.py
```

### 4. 访问应用
打开浏览器访问: http://localhost:5000

## 使用说明

### 语音对话模式
1. 点击"语音对话"标签页
2. 输入角色ID（或使用默认值）
3. 点击"初始化连接"
4. 使用以下方式进行对话：
   - **语音输入**: 点击录音按钮，说话后再次点击停止
   - **文本输入**: 在文本框中输入内容，按回车或点击发送

### TTS模式
1. 点击"文本转语音"标签页
2. 配置音色ID、语速、音量等参数
3. 点击"初始化"
4. 在文本框中输入要转换的文本
5. 点击"生成语音"

## 配置说明

Web客户端使用项目根目录的 `utils/config_manager.py` 中的配置：

```python
# 实时对话API配置
"realtime": {
    "base_url": "wss://mcgpt-test.mach-drive.com/openapigamma/v1/realtime-server?model=step-1o-voice&audio_type=pcm",
    "token": "your_realtime_token"
},

# TTS API配置
"tts": {
    "base_url": "wss://mcgpt-test.mach-drive.com/openapitts/v1/realtime-tts",
    "token": "your_tts_token"
}
```

## API集成

### WebSocket事件

#### 客户端发送事件
- `init_realtime`: 初始化实时对话连接
- `init_tts`: 初始化TTS连接
- `send_audio`: 发送音频数据
- `create_realtime_response`: 创建实时对话响应
- `send_tts_text`: 发送TTS文本

#### 服务器发送事件
- `realtime_initialized`: 实时对话初始化结果
- `tts_initialized`: TTS初始化结果
- `realtime_text_delta`: 实时文本增量
- `realtime_audio_delta`: 实时音频增量
- `tts_audio_complete`: TTS音频生成完成

## 故障排除

### 常见问题

1. **无法录音**
   - 检查浏览器是否允许麦克风权限
   - 确保使用HTTPS或localhost访问

2. **连接失败**
   - 检查网络连接
   - 验证API地址和token配置
   - 查看系统日志获取详细错误信息

3. **音频播放问题**
   - 检查浏览器音频权限
   - 确保音频格式支持
   - 尝试刷新页面重新连接

### 调试模式
启动时添加调试参数：
```bash
python3 app.py --debug
```

## 开发说明

### 项目结构
```
webapp/
├── app.py              # 主服务器文件
├── templates/
│   └── index.html      # 主页面模板
├── static/
│   └── js/
│       └── app.js      # 前端JavaScript
├── requirements.txt    # Python依赖
├── start.sh           # 启动脚本
└── README.md          # 说明文档
```

### 扩展功能
- 添加用户认证
- 支持多用户并发
- 添加对话记录保存
- 集成更多AI模型
- 添加音频格式转换

## 许可证

本项目遵循与主项目相同的许可证。
