#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试Web Client依赖是否正确安装
"""

import sys
import os

def test_imports():
    """测试所有必要的导入"""
    print("测试Python模块导入...")
    
    try:
        import flask
        print("✓ Flask 导入成功")
    except ImportError as e:
        print(f"✗ Flask 导入失败: {e}")
        return False
    
    try:
        import flask_socketio
        print("✓ Flask-SocketIO 导入成功")
    except ImportError as e:
        print(f"✗ Flask-SocketIO 导入失败: {e}")
        return False
    
    try:
        import websockets
        print("✓ websockets 导入成功")
    except ImportError as e:
        print(f"✗ websockets 导入失败: {e}")
        return False
    
    return True

def test_project_imports():
    """测试项目模块导入"""
    print("\n测试项目模块导入...")
    
    # 添加项目根目录到Python路径
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    sys.path.append(project_root)
    
    try:
        from workflow.realtime_api import RealtimeEventHandler
        print("✓ RealtimeEventHandler 导入成功")
    except ImportError as e:
        print(f"✗ RealtimeEventHandler 导入失败: {e}")
        return False
    
    try:
        from workflow.tts_api import TTSEventHandler
        print("✓ TTSEventHandler 导入成功")
    except ImportError as e:
        print(f"✗ TTSEventHandler 导入失败: {e}")
        return False
    
    try:
        from utils.config_manager import get_config_manager
        print("✓ ConfigManager 导入成功")
    except ImportError as e:
        print(f"✗ ConfigManager 导入失败: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("=== Vocal Auto Web Client 依赖测试 ===\n")
    
    tests = [
        ("Python模块导入", test_imports),
        ("项目模块导入", test_project_imports),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    print("\n=== 测试结果汇总 ===")
    all_passed = True
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！Web Client可以启动。")
    else:
        print("\n❌ 部分测试失败，请先安装依赖。")
        print("安装命令: pip install Flask Flask-SocketIO websockets")
    
    return all_passed

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
