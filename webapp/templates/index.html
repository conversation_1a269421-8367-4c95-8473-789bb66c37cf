<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vocal Auto Web Client</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .chat-container {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 15px;
            background-color: #f8f9fa;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 10px;
        }
        .user-message {
            background-color: #007bff;
            color: white;
            margin-left: 20%;
        }
        .assistant-message {
            background-color: #e9ecef;
            color: #333;
            margin-right: 20%;
        }
        .recording {
            background-color: #dc3545;
            color: white;
        }
        .control-panel {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected {
            background-color: #28a745;
        }
        .status-disconnected {
            background-color: #dc3545;
        }
        .status-connecting {
            background-color: #ffc107;
        }
        .audio-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 10px;
        }
        .record-button {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: none;
            font-size: 24px;
            transition: all 0.3s;
        }
        .record-button:not(.recording) {
            background-color: #007bff;
            color: white;
        }
        .record-button.recording {
            background-color: #dc3545;
            color: white;
            animation: pulse 1s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        .mode-tabs {
            margin-bottom: 20px;
        }
        .config-section {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-microphone"></i>
            Vocalauto Web Client
        </h1>
        
        <!-- 连接状态 -->
        <div class="control-panel">
            <h5>连接状态</h5>
            <div id="connection-status">
                <span class="status-indicator status-disconnected"></span>
                <span id="status-text">未连接</span>
            </div>
        </div>
        
        <!-- 模式选择 -->
        <ul class="nav nav-tabs mode-tabs" id="mode-tabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="realtime-tab" data-bs-toggle="tab" data-bs-target="#realtime-panel" type="button" role="tab">
                    <i class="fas fa-comments"></i> 语音对话 (Realtime)
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="tts-tab" data-bs-toggle="tab" data-bs-target="#tts-panel" type="button" role="tab">
                    <i class="fas fa-volume-up"></i> 文本转语音 (TTS)
                </button>
            </li>
        </ul>
        
        <div class="tab-content" id="mode-content">
            <!-- 语音对话面板 -->
            <div class="tab-pane fade show active" id="realtime-panel" role="tabpanel">
                <!-- 配置区域 -->
                <div class="config-section">
                    <h6><i class="fas fa-cog"></i> 实时对话配置</h6>

                    <!-- 第一行：环境配置和角色ID -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <label for="env-config" class="form-label">环境配置:</label>
                            <select class="form-select" id="env-config">
                                <option value="test" selected>Test环境</option>
                                <option value="uat">UAT环境</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="realtime-role-id" class="form-label">角色ID:</label>
                            <select class="form-select" id="realtime-role-id">
                                <option value="yinhejingling" selected>银河精灵</option>
                                <option value="dushexiaoyu">毒舌小宇</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="realtime-pattern" class="form-label">模式:</label>
                            <select class="form-select" id="realtime-pattern">
                                <option value="normal" selected>Normal</option>
                                <option value="child">Child</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="output-language" class="form-label">输出语言:</label>
                            <select class="form-select" id="output-language">
                                <option value="" selected>普通话</option>
                                <option value="粤语">粤语</option>
                            </select>
                        </div>
                    </div>

                    <!-- 第二行：音量、语速和操作 -->
                    <div class="row">
                        <div class="col-md-3">
                            <label for="realtime-volume" class="form-label">音量:</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="realtime-volume" value="1.0" min="0.1" max="2.0" step="0.1">
                                <span class="input-group-text">倍</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label for="realtime-speed" class="form-label">语速:</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="realtime-speed" value="1.0" min="0.5" max="2.0" step="0.1">
                                <span class="input-group-text">倍</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">操作:</label>
                            <div class="d-flex gap-2">
                                <button class="btn btn-success" id="setup-realtime-btn">
                                    <i class="fas fa-comments"></i> 开始对话
                                </button>
                                <button class="btn btn-outline-secondary" id="reset-realtime-config-btn">
                                    <i class="fas fa-undo"></i> 重置配置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 对话区域 -->
                <div class="chat-container" id="realtime-chat">
                    <div class="text-center text-muted">
                        <i class="fas fa-comments fa-3x mb-3"></i>
                        <p>设置配置后即可开始语音对话</p>
                        <p><small>连接将在首次发送消息时自动建立</small></p>
                    </div>
                </div>
                
                <!-- 音频控制 -->
                <div class="audio-controls">
                    <button class="record-button" id="record-btn" disabled>
                        <i class="fas fa-microphone"></i>
                    </button>
                    <div class="flex-grow-1">
                        <input type="text" class="form-control" id="transcript-input" placeholder="或者直接输入文本..." disabled>
                    </div>
                    <button class="btn btn-success" id="send-realtime-btn" disabled>
                        <i class="fas fa-paper-plane"></i> 发送
                    </button>
                </div>
            </div>
            
            <!-- TTS面板 -->
            <div class="tab-pane fade" id="tts-panel" role="tabpanel">
                <!-- 配置区域 -->
                <div class="config-section">
                    <h6><i class="fas fa-cog"></i> TTS配置</h6>

                    <!-- 第一行：环境配置和音色ID -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <label for="tts-env-config" class="form-label">环境配置:</label>
                            <select class="form-select" id="tts-env-config">
                                <option value="test" selected>Test环境</option>
                                <option value="uat">UAT环境</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="tts-voice-id" class="form-label">音色ID:</label>
                            <select class="form-select" id="tts-voice-id">
                                <option value="yinhejingling" selected>银河精灵</option>
                                <option value="dushexiaoyu">毒舌小宇</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="tts-emotion" class="form-label">情感:</label>
                            <select class="form-select" id="tts-emotion">
                                <option value="" selected>默认</option>
                                <option value="happy">开心</option>
                                <option value="sad">悲伤</option>
                                <option value="angry">愤怒</option>
                                <option value="calm">平静</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="tts-language" class="form-label">语言:</label>
                            <select class="form-select" id="tts-language">
                                <option value="" selected>普通话</option>
                                <option value="粤语">粤语</option>
                            </select>
                        </div>
                    </div>

                    <!-- 第二行：语速、音量和操作 -->
                    <div class="row">
                        <div class="col-md-3">
                            <label for="tts-speed" class="form-label">语速:</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="tts-speed" value="1.0" min="0.5" max="2.0" step="0.1">
                                <span class="input-group-text">倍</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label for="tts-volume" class="form-label">音量:</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="tts-volume" value="1.0" min="0.1" max="2.0" step="0.1">
                                <span class="input-group-text">倍</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">操作:</label>
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary" id="init-tts-btn">
                                    <i class="fas fa-play"></i> 初始化
                                </button>
                                <button class="btn btn-outline-secondary" id="reset-tts-config-btn">
                                    <i class="fas fa-undo"></i> 重置配置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- TTS输入区域 -->
                <div class="mb-3">
                    <label for="tts-text" class="form-label">输入文本:</label>
                    <textarea class="form-control" id="tts-text" rows="4" placeholder="输入要转换为语音的文本..." disabled></textarea>
                </div>
                
                <div class="d-flex gap-2 mb-3">
                    <button class="btn btn-success" id="send-tts-btn" disabled>
                        <i class="fas fa-volume-up"></i> 生成语音
                    </button>
                    <button class="btn btn-secondary" id="clear-tts-btn">
                        <i class="fas fa-trash"></i> 清空
                    </button>
                </div>
                
                <!-- 音频播放区域 -->
                <div id="tts-audio-container">
                    <!-- 生成的音频将在这里显示 -->
                </div>
            </div>
        </div>
        
        <!-- 日志区域 -->
        <div class="mt-4">
            <h5>
                <i class="fas fa-list"></i> 系统日志
                <button class="btn btn-sm btn-outline-secondary float-end" id="clear-log-btn">
                    <i class="fas fa-trash"></i> 清空日志
                </button>
            </h5>
            <div class="chat-container" id="system-log" style="height: 200px; font-family: monospace; font-size: 12px;">
                <!-- 系统日志将在这里显示 -->
            </div>
        </div>
    </div>
    
    <!-- 音频播放器 (隐藏) -->
    <audio id="audio-player" controls style="display: none;"></audio>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
