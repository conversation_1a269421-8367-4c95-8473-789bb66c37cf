#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import asyncio
import json
import base64
import io
import tempfile
from flask import Flask, render_template, request, jsonify
from flask_socketio import SocketIO, emit
import threading
import uuid
import time
from pydub import AudioSegment

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from workflow.realtime_api import RealtimeEventHandler
from workflow.tts_api import TTSEventHandler
from utils.config_manager import get_config_manager
from utils.session_manager import SessionManager

app = Flask(__name__)
app.config['SECRET_KEY'] = 'vocal_auto_web_client'
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

# 全局配置
config_manager = get_config_manager()

def get_config_for_env(env_config):
    """根据环境配置获取对应的配置管理器"""
    import os

    # 获取当前文件的目录，然后找到项目根目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)  # 上级目录

    if env_config == 'test':
        config_file = os.path.join(project_root, 'config', 'test.env.json')
    elif env_config == 'uat':
        config_file = os.path.join(project_root, 'config', 'uat.env.json')
    else:
        config_file = None

    print(f"加载环境配置: {env_config}, 配置文件: {config_file}")

    # 检查文件是否存在
    if config_file and not os.path.exists(config_file):
        print(f"配置文件不存在: {config_file}")
        print(f"当前工作目录: {os.getcwd()}")
        print(f"项目根目录: {project_root}")
        return None

    config_manager = get_config_manager(config_file, force_reload=True)

    # 验证配置是否正确加载
    realtime_url = config_manager.get('realtime.base_url')
    print(f"实际加载的realtime URL: {realtime_url}")

    return config_manager

def convert_webm_to_ogg_speex(webm_data):
    """将WebM音频数据转换为OGG Speex 16k格式"""
    try:
        print(f"开始音频转换，输入数据大小: {len(webm_data)} bytes")

        # 检查pydub是否可用
        try:
            from pydub import AudioSegment
        except ImportError as e:
            print(f"pydub库未安装: {e}")
            print("请运行: pip install pydub")
            return None

        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix='.webm', delete=False) as temp_webm:
            temp_webm.write(webm_data)
            temp_webm_path = temp_webm.name

        print(f"临时文件创建: {temp_webm_path}")

        try:
            # 使用pydub加载音频
            print("正在加载音频文件...")
            audio = AudioSegment.from_file(temp_webm_path)
            print(f"原始音频: 采样率={audio.frame_rate}Hz, 声道={audio.channels}, 时长={len(audio)}ms")

            # 转换为16k单声道
            print("正在转换音频格式...")
            audio = audio.set_frame_rate(16000)
            audio = audio.set_channels(1)
            audio = audio.set_sample_width(2)  # 16位

            print(f"转换后音频: 采样率={audio.frame_rate}Hz, 声道={audio.channels}")

            # 导出为OGG Speex格式
            print("正在导出为OGG Speex格式...")
            ogg_io = io.BytesIO()

            try:
                audio.export(ogg_io, format="ogg", codec="libspeex")
                ogg_data = ogg_io.getvalue()
                print(f"OGG Speex转换成功，输出大小: {len(ogg_data)} bytes")
                return ogg_data
            except Exception as export_error:
                print(f"OGG Speex导出失败: {export_error}")
                print("尝试使用PCM格式...")

                # 如果Speex失败，尝试PCM格式
                pcm_io = io.BytesIO()
                audio.export(pcm_io, format="s16le", codec="pcm_s16le")
                pcm_data = pcm_io.getvalue()
                print(f"PCM转换成功，输出大小: {len(pcm_data)} bytes")
                return pcm_data
            finally:
                ogg_io.close()

        finally:
            # 清理临时文件
            if os.path.exists(temp_webm_path):
                os.unlink(temp_webm_path)
                print("临时文件已清理")

    except Exception as e:
        print(f"音频转换失败: {e}")
        import traceback
        traceback.print_exc()
        return None

# 存储活跃的连接
active_connections = {}

class WebSocketProxy:
    """WebSocket代理类，处理前端和后端API的通信"""

    def __init__(self, session_id):
        self.session_id = session_id
        self.realtime_config = None
        self.tts_config = None
        self.loop = None
        self.thread = None
        self.username = f"web_user_{session_id[-8:]}"  # 使用session_id后8位作为用户名
        
    def start_event_loop(self):
        """在新线程中启动事件循环"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        self.loop.run_forever()
        
    def stop_event_loop(self):
        """停止事件循环"""
        if self.loop:
            self.loop.call_soon_threadsafe(self.loop.stop)
        if self.thread:
            self.thread.join()
            
    def setup_realtime_config(self, env_config, role_id, pattern="normal", volume_ratio=1.0, speed_ratio=1.0, output_language=None):
        """设置实时对话配置（不建立连接）"""
        try:
            # 获取对应环境的配置
            env_config_manager = get_config_for_env(env_config)

            # 统一使用OGG格式，因为我们的音频转换输出OGG Speex
            audio_format = 'ogg'
            base_url = env_config_manager.get('realtime.base_url').format(audio_format=audio_format)
            token = env_config_manager.get('realtime.token')
            headers = {'Authorization': f'Bearer {token}'}

            # 保存配置信息，延迟到真正使用时再连接
            self.realtime_config = {
                'base_url': base_url,
                'headers': headers,
                'role_id': role_id,
                'pattern': pattern,
                'volume_ratio': volume_ratio,
                'speed_ratio': speed_ratio,
                'output_language': output_language,
                'audio_format': audio_format
            }

            print(f"实时对话配置已设置: 环境={env_config}, 角色={role_id}, 音频格式={audio_format}")
            print(f"API地址: {base_url}")
            return True
        except Exception as e:
            print(f"设置实时对话配置失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    async def send_text_and_get_response(self, text, conversation_history=[]):
        """发送文本并获取完整响应（每轮对话独立连接）"""
        realtime_handler = None
        api_session_id = None

        try:
            # 创建新的session_id
            api_session_id = SessionManager.create_session_id(self.username)
            session_url = SessionManager.create_session_url(api_session_id)
            print(f"创建新会话: {api_session_id}")
            print(f"会话URL: {session_url}")

            # 构建完整的headers
            complete_headers = {
                'Authorization': self.realtime_config['headers']['Authorization'],
                'OpenAI-Beta': 'realtime=v1',
                'Session-ID': api_session_id,
                'Content-Type': 'application/json; charset=UTF-8'
            }

            # 创建新的连接
            print("=== 建立新的WebSocket连接 ===")
            print(f"URL: {self.realtime_config['base_url']}")
            print(f"Headers: {complete_headers}")
            print("==============================")

            realtime_handler = RealtimeEventHandler(
                self.realtime_config['base_url'],
                complete_headers,
                username=self.username
            )
            await realtime_handler.connect()
            print("WebSocket连接建立成功")

            # 添加用户文本到对话历史
            current_conversation = conversation_history + [{
                "role": "user",
                "type": "text",
                "text": text
            }]

            # 创建响应
            config = self.realtime_config
            print("=== 创建文本对话响应 ===")
            print(f"role_id: {config['role_id']}")
            print(f"pattern: {config['pattern']}")
            print(f"text: {text}")
            print(f"conversation_history: {current_conversation}")
            print("========================")

            await realtime_handler.create_multi_response(
                role_id=config['role_id'],
                pattern=config['pattern'],
                conversation_history=current_conversation,
                volume_ratio=config['volume_ratio'],
                speed_ratio=config['speed_ratio'],
                output_language=config['output_language']
            )

            # 接收响应
            response_text = ""
            audio_chunks = []

            while True:
                response = await realtime_handler.receive()
                print(f"收到API响应: {response}")  # 添加调试信息

                if response.get('type') == 'response.audio_transcript.delta':
                    delta_text = response.get('delta', '')
                    if delta_text != '\x00':  # 检查 delta 是否为 '\x00'
                        response_text += delta_text
                        print(f"收到文本增量: '{delta_text}'")  # 添加调试信息
                        # 发送文本增量到前端
                        socketio.emit('realtime_text_delta', {
                            'text': delta_text,
                            'session_id': self.session_id
                        })

                elif response.get('type') == 'response.audio.delta':
                    audio_data = response.get('delta', '')
                    if audio_data:
                        audio_chunks.append(audio_data)
                        print(f"收到音频增量，大小: {len(audio_data)}")  # 添加调试信息
                        # 发送音频增量到前端
                        socketio.emit('realtime_audio_delta', {
                            'audio': audio_data,
                            'session_id': self.session_id
                        })

                elif response.get('type') == 'response.done':
                    print("API响应完成")  # 添加调试信息
                    break
                elif response.get('type') == 'error':
                    print(f"API返回错误: {response}")
                    break
                else:
                    print(f"未处理的响应类型: {response.get('type')}")  # 添加调试信息

            print(f"文本对话完成，响应文本长度: {len(response_text)}, 音频片段数: {len(audio_chunks)}")

            # 如果没有收到文本响应，添加一个默认回复用于调试
            if not response_text:
                response_text = f"[调试] API连接成功，但未收到文本响应。Session: {api_session_id}"
                print("API未返回文本内容，使用默认回复")

            return True, {
                'text': response_text,
                'audio_chunks': audio_chunks,
                'session_id': api_session_id,
                'session_url': session_url
            }, None

        except Exception as e:
            print(f"文本对话失败: {e}")
            import traceback
            traceback.print_exc()
            return False, None, str(e)

        finally:
            # 确保连接被关闭
            if realtime_handler:
                try:
                    await realtime_handler.close()
                    print("WebSocket连接已关闭")
                except Exception as e:
                    print(f"关闭连接时出错: {e}")
            
    async def init_tts(self, env_config, voice_id, speed_ratio=1.0, volume_ratio=1.0, emotion=None, language=None):
        """初始化TTS连接"""
        try:
            # 获取对应环境的配置
            env_config_manager = get_config_for_env(env_config)
            base_url = env_config_manager.get('tts.base_url')
            token = env_config_manager.get('tts.token')
            headers = {'Authorization': f'Bearer {token}'}

            # 创建处理器
            self.tts_handler = TTSEventHandler(base_url, headers)
            await self.tts_handler.connect()
            await self.tts_handler.create_session(
                voice_id=voice_id,
                speed_ratio=speed_ratio,
                volume_ratio=volume_ratio,
                emotion=emotion,
                language=language
            )

            return True
        except Exception as e:
            print(f"初始化TTS失败: {e}")
            return False
            
    async def send_audio_and_get_response(self, audio_data_base64, transcript, conversation_history=[]):
        """发送音频数据并获取完整响应（每轮对话独立连接）"""
        realtime_handler = None
        api_session_id = None

        try:
            # 解码base64音频数据
            print("正在解码音频数据...")
            audio_data = base64.b64decode(audio_data_base64)
            print(f"解码完成，音频数据大小: {len(audio_data)} bytes")

            # 转换为OGG Speex 16k格式
            print("正在转换音频格式为OGG Speex 16k...")
            converted_data = convert_webm_to_ogg_speex(audio_data)
            if not converted_data:
                print("音频格式转换失败")
                return False, None, None

            # 重新编码为base64
            converted_base64 = base64.b64encode(converted_data).decode('utf-8')
            print(f"音频转换完成，准备发送到API")

            # 创建新的session_id
            api_session_id = SessionManager.create_session_id(self.username)
            session_url = SessionManager.create_session_url(api_session_id)
            print(f"创建新会话: {api_session_id}")
            print(f"会话URL: {session_url}")

            # 构建完整的headers
            complete_headers = {
                'Authorization': self.realtime_config['headers']['Authorization'],
                'OpenAI-Beta': 'realtime=v1',
                'Session-ID': api_session_id,
                'Content-Type': 'application/json; charset=UTF-8'
            }

            # 创建新的连接
            print("=== 建立新的WebSocket连接 ===")
            print(f"URL: {self.realtime_config['base_url']}")
            print(f"Headers: {complete_headers}")
            print("==============================")

            realtime_handler = RealtimeEventHandler(
                self.realtime_config['base_url'],
                complete_headers,
                username=self.username
            )
            await realtime_handler.connect()
            print("WebSocket连接建立成功")

            # 发送音频
            turn_id = str(uuid.uuid4())
            print("=== 发送音频到API ===")
            print(f"turn_id: {turn_id}")
            print(f"transcript: '{transcript}'")
            print(f"音频数据长度: {len(converted_base64)} chars")
            print("====================")

            await realtime_handler.append_audio_buffer(converted_base64, turn_id)
            await realtime_handler.commit_audio_buffer(turn_id, transcript)
            print("音频发送完成")

            # 创建响应
            config = self.realtime_config
            print("=== 创建实时对话响应 ===")
            print(f"role_id: {config['role_id']}")
            print(f"pattern: {config['pattern']}")
            print(f"volume_ratio: {config['volume_ratio']}")
            print(f"speed_ratio: {config['speed_ratio']}")
            print(f"output_language: {config['output_language']}")
            print(f"conversation_history: {conversation_history}")
            print("========================")

            await realtime_handler.create_multi_response(
                role_id=config['role_id'],
                pattern=config['pattern'],
                conversation_history=conversation_history,
                volume_ratio=config['volume_ratio'],
                speed_ratio=config['speed_ratio'],
                output_language=config['output_language']
            )

            # 接收响应
            response_text = ""
            audio_chunks = []

            while True:
                response = await realtime_handler.receive()
                print(f"收到API响应: {response}")  # 添加调试信息

                if response.get('type') == 'response.audio_transcript.delta':
                    delta_text = response.get('delta', '')
                    if delta_text != '\x00':  # 检查 delta 是否为 '\x00'
                        response_text += delta_text
                        print(f"收到文本增量: '{delta_text}'")  # 添加调试信息
                        # 发送文本增量到前端
                        socketio.emit('realtime_text_delta', {
                            'text': delta_text,
                            'session_id': self.session_id
                        })

                elif response.get('type') == 'response.audio.delta':
                    audio_data = response.get('delta', '')
                    if audio_data:
                        audio_chunks.append(audio_data)
                        print(f"收到音频增量，大小: {len(audio_data)}")  # 添加调试信息
                        # 发送音频增量到前端
                        socketio.emit('realtime_audio_delta', {
                            'audio': audio_data,
                            'session_id': self.session_id
                        })

                elif response.get('type') == 'response.done':
                    print("API响应完成")  # 添加调试信息
                    break
                elif response.get('type') == 'error':
                    print(f"API返回错误: {response}")
                    break
                else:
                    print(f"未处理的响应类型: {response.get('type')}")  # 添加调试信息

            print(f"对话完成，响应文本长度: {len(response_text)}, 音频片段数: {len(audio_chunks)}")

            # 如果没有收到文本响应，添加一个默认回复用于调试
            if not response_text:
                response_text = f"[调试] API连接成功，但未收到文本响应。Session: {api_session_id}"
                print("API未返回文本内容，使用默认回复")

            return True, {
                'text': response_text,
                'audio_chunks': audio_chunks,
                'session_id': api_session_id,
                'session_url': session_url
            }, None

        except Exception as e:
            print(f"音频对话失败: {e}")
            import traceback
            traceback.print_exc()
            return False, None, str(e)

        finally:
            # 确保连接被关闭
            if realtime_handler:
                try:
                    await realtime_handler.close()
                    print("WebSocket连接已关闭")
                except Exception as e:
                    print(f"关闭连接时出错: {e}")
            
    async def close(self):
        """关闭连接（现在不需要保持长连接）"""
        print("WebSocket代理关闭（每轮对话独立连接）")
            
    async def send_tts_text(self, text):
        """发送文本到TTS API"""
        if not self.tts_handler:
            return None
            
        try:
            # 发送文本
            await self.tts_handler.send_text(text)
            
            # 接收音频
            audio_chunks = []
            
            def audio_callback(audio_data):
                audio_base64 = base64.b64encode(audio_data).decode('utf-8')
                audio_chunks.append(audio_base64)
                # 发送音频到前端
                socketio.emit('tts_audio_complete', {
                    'audio': audio_base64,
                    'session_id': self.session_id
                })
                
            await self.tts_handler.receive_audio(audio_callback)
            
            return audio_chunks
            
        except Exception as e:
            print(f"TTS文本转语音失败: {e}")
            return None
            
    async def close(self):
        """关闭连接"""
        if self.realtime_handler:
            await self.realtime_handler.close()
        if self.tts_handler:
            await self.tts_handler.close()

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@socketio.on('connect')
def handle_connect():
    """处理客户端连接"""
    session_id = request.sid
    print(f"客户端连接: {session_id}")
    
    # 创建WebSocket代理
    proxy = WebSocketProxy(session_id)
    proxy.thread = threading.Thread(target=proxy.start_event_loop)
    proxy.thread.start()
    
    active_connections[session_id] = proxy
    
    emit('connected', {'session_id': session_id})

@socketio.on('disconnect')
def handle_disconnect():
    """处理客户端断开连接"""
    session_id = request.sid
    print(f"客户端断开连接: {session_id}")
    
    if session_id in active_connections:
        proxy = active_connections[session_id]
        
        # 在事件循环中关闭连接
        if proxy.loop:
            asyncio.run_coroutine_threadsafe(proxy.close(), proxy.loop)
        
        proxy.stop_event_loop()
        del active_connections[session_id]

@socketio.on('setup_realtime')
def handle_setup_realtime(data):
    """设置实时对话配置"""
    session_id = request.sid
    env_config = data.get('env_config', 'test')
    role_id = data.get('role_id', 'yinhejingling')
    pattern = data.get('pattern', 'normal')
    volume_ratio = data.get('volume_ratio', 1.0)
    speed_ratio = data.get('speed_ratio', 1.0)
    output_language = data.get('output_language', None)

    if session_id in active_connections:
        proxy = active_connections[session_id]

        success = proxy.setup_realtime_config(
            env_config, role_id, pattern, volume_ratio, speed_ratio, output_language
        )

        socketio.emit('realtime_configured', {
            'success': success,
            'session_id': session_id
        })

@socketio.on('init_tts')
def handle_init_tts(data):
    """初始化TTS"""
    session_id = request.sid
    env_config = data.get('env_config', 'test')
    voice_id = data.get('voice_id', 'yinhejingling')
    speed_ratio = data.get('speed_ratio', 1.0)
    volume_ratio = data.get('volume_ratio', 1.0)
    emotion = data.get('emotion', None)
    language = data.get('language', None)

    if session_id in active_connections:
        proxy = active_connections[session_id]

        async def init_task():
            success = await proxy.init_tts(env_config, voice_id, speed_ratio, volume_ratio, emotion, language)
            socketio.emit('tts_initialized', {
                'success': success,
                'session_id': session_id
            })

        asyncio.run_coroutine_threadsafe(init_task(), proxy.loop)

@socketio.on('send_audio')
def handle_send_audio(data):
    """处理音频发送（每轮对话独立连接）"""
    session_id = request.sid
    audio_data = data.get('audio')
    transcript = data.get('transcript', '')
    conversation_history = data.get('conversation_history', [])

    print(f"收到音频数据，大小: {len(audio_data) if audio_data else 0} chars, transcript: '{transcript}'")

    if session_id in active_connections:
        proxy = active_connections[session_id]

        async def send_task():
            success, result, error = await proxy.send_audio_and_get_response(
                audio_data, transcript, conversation_history
            )

            if success:
                socketio.emit('audio_conversation_complete', {
                    'success': True,
                    'session_id': session_id,
                    'result': result,
                    'message': '音频对话完成'
                })
            else:
                socketio.emit('audio_conversation_complete', {
                    'success': False,
                    'session_id': session_id,
                    'error': error,
                    'message': '音频对话失败'
                })

        asyncio.run_coroutine_threadsafe(send_task(), proxy.loop)

@socketio.on('send_text')
def handle_send_text(data):
    """处理文本发送（每轮对话独立连接）"""
    session_id = request.sid
    text = data.get('text', '')
    conversation_history = data.get('conversation_history', [])

    print(f"收到文本消息: '{text}'")

    if session_id in active_connections:
        proxy = active_connections[session_id]

        async def text_task():
            success, result, error = await proxy.send_text_and_get_response(
                text, conversation_history
            )

            if success:
                socketio.emit('text_conversation_complete', {
                    'success': True,
                    'session_id': session_id,
                    'result': result,
                    'message': '文本对话完成'
                })
            else:
                socketio.emit('text_conversation_complete', {
                    'success': False,
                    'session_id': session_id,
                    'error': error,
                    'message': '文本对话失败'
                })

        asyncio.run_coroutine_threadsafe(text_task(), proxy.loop)

@socketio.on('send_tts_text')
def handle_send_tts_text(data):
    """处理TTS文本发送"""
    session_id = request.sid
    text = data.get('text', '')
    
    if session_id in active_connections:
        proxy = active_connections[session_id]
        
        async def tts_task():
            result = await proxy.send_tts_text(text)
            socketio.emit('tts_complete', {
                'result': result,
                'session_id': session_id
            })
            
        asyncio.run_coroutine_threadsafe(tts_task(), proxy.loop)

if __name__ == '__main__':
    print("启动Vocal Auto Web Client...")
    print("访问地址: http://localhost:5000")
    socketio.run(app, host='0.0.0.0', port=5000, debug=True)
