#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import asyncio
import json
import base64
from flask import Flask, render_template, request, jsonify
from flask_socketio import SocketIO, emit
import threading
import uuid
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from workflow.realtime_api import RealtimeEventHandler
from workflow.tts_api import TTSEventHandler
from utils.config_manager import get_config_manager

app = Flask(__name__)
app.config['SECRET_KEY'] = 'vocal_auto_web_client'
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

# 全局配置
config_manager = get_config_manager()

# 存储活跃的连接
active_connections = {}

class WebSocketProxy:
    """WebSocket代理类，处理前端和后端API的通信"""
    
    def __init__(self, session_id):
        self.session_id = session_id
        self.realtime_handler = None
        self.tts_handler = None
        self.loop = None
        self.thread = None
        
    def start_event_loop(self):
        """在新线程中启动事件循环"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        self.loop.run_forever()
        
    def stop_event_loop(self):
        """停止事件循环"""
        if self.loop:
            self.loop.call_soon_threadsafe(self.loop.stop)
        if self.thread:
            self.thread.join()
            
    async def init_realtime(self, role_id):
        """初始化实时对话连接"""
        try:
            # 获取配置
            base_url = config_manager.get('realtime.base_url').format(audio_format='pcm')
            token = config_manager.get('realtime.token')
            headers = {'Authorization': f'Bearer {token}'}
            
            # 创建处理器
            self.realtime_handler = RealtimeEventHandler(base_url, headers)
            await self.realtime_handler.connect()
            
            return True
        except Exception as e:
            print(f"初始化实时对话失败: {e}")
            return False
            
    async def init_tts(self, voice_id, speed_ratio=1.0, volume_ratio=1.0):
        """初始化TTS连接"""
        try:
            # 获取配置
            base_url = config_manager.get('tts.base_url')
            token = config_manager.get('tts.token')
            headers = {'Authorization': f'Bearer {token}'}
            
            # 创建处理器
            self.tts_handler = TTSEventHandler(base_url, headers)
            await self.tts_handler.connect()
            await self.tts_handler.create_session(
                voice_id=voice_id,
                speed_ratio=speed_ratio,
                volume_ratio=volume_ratio
            )
            
            return True
        except Exception as e:
            print(f"初始化TTS失败: {e}")
            return False
            
    async def send_audio(self, audio_data, transcript):
        """发送音频数据到实时对话API"""
        if not self.realtime_handler:
            return False
            
        try:
            turn_id = str(uuid.uuid4())
            await self.realtime_handler.append_audio_buffer(audio_data, turn_id)
            await self.realtime_handler.commit_audio_buffer(turn_id, transcript)
            return True
        except Exception as e:
            print(f"发送音频失败: {e}")
            return False
            
    async def create_realtime_response(self, role_id, conversation_history):
        """创建实时对话响应"""
        if not self.realtime_handler:
            return None

        try:
            await self.realtime_handler.create_multi_response(
                role_id=role_id,
                conversation_history=conversation_history
            )

            # 接收响应
            response_text = ""
            audio_chunks = []

            while True:
                response = await self.realtime_handler.receive()

                if response.get('type') == 'response.text.delta':
                    response_text += response.get('delta', '')
                    # 发送文本增量到前端
                    socketio.emit('realtime_text_delta', {
                        'text': response.get('delta', ''),
                        'session_id': self.session_id
                    })

                elif response.get('type') == 'response.audio.delta':
                    audio_data = response.get('audio', '')
                    if audio_data:
                        audio_chunks.append(audio_data)
                        # 发送音频增量到前端
                        socketio.emit('realtime_audio_delta', {
                            'audio': audio_data,
                            'session_id': self.session_id
                        })

                elif response.get('type') == 'response.done':
                    break
                elif response.get('type') == 'error':
                    print(f"API返回错误: {response}")
                    break

            return {
                'text': response_text,
                'audio_chunks': audio_chunks
            }

        except Exception as e:
            print(f"创建实时对话响应失败: {e}")
            socketio.emit('error', {
                'message': f"实时对话失败: {str(e)}",
                'session_id': self.session_id
            })
            return None
            
    async def send_tts_text(self, text):
        """发送文本到TTS API"""
        if not self.tts_handler:
            return None
            
        try:
            # 发送文本
            await self.tts_handler.send_text(text)
            
            # 接收音频
            audio_chunks = []
            
            def audio_callback(audio_data):
                audio_base64 = base64.b64encode(audio_data).decode('utf-8')
                audio_chunks.append(audio_base64)
                # 发送音频到前端
                socketio.emit('tts_audio_complete', {
                    'audio': audio_base64,
                    'session_id': self.session_id
                })
                
            await self.tts_handler.receive_audio(audio_callback)
            
            return audio_chunks
            
        except Exception as e:
            print(f"TTS文本转语音失败: {e}")
            return None
            
    async def close(self):
        """关闭连接"""
        if self.realtime_handler:
            await self.realtime_handler.close()
        if self.tts_handler:
            await self.tts_handler.close()

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@socketio.on('connect')
def handle_connect():
    """处理客户端连接"""
    session_id = request.sid
    print(f"客户端连接: {session_id}")
    
    # 创建WebSocket代理
    proxy = WebSocketProxy(session_id)
    proxy.thread = threading.Thread(target=proxy.start_event_loop)
    proxy.thread.start()
    
    active_connections[session_id] = proxy
    
    emit('connected', {'session_id': session_id})

@socketio.on('disconnect')
def handle_disconnect():
    """处理客户端断开连接"""
    session_id = request.sid
    print(f"客户端断开连接: {session_id}")
    
    if session_id in active_connections:
        proxy = active_connections[session_id]
        
        # 在事件循环中关闭连接
        if proxy.loop:
            asyncio.run_coroutine_threadsafe(proxy.close(), proxy.loop)
        
        proxy.stop_event_loop()
        del active_connections[session_id]

@socketio.on('init_realtime')
def handle_init_realtime(data):
    """初始化实时对话"""
    session_id = request.sid
    role_id = data.get('role_id', 'default_role')
    
    if session_id in active_connections:
        proxy = active_connections[session_id]
        
        async def init_task():
            success = await proxy.init_realtime(role_id)
            socketio.emit('realtime_initialized', {
                'success': success,
                'session_id': session_id
            })
            
        asyncio.run_coroutine_threadsafe(init_task(), proxy.loop)

@socketio.on('init_tts')
def handle_init_tts(data):
    """初始化TTS"""
    session_id = request.sid
    voice_id = data.get('voice_id', 'default_voice')
    speed_ratio = data.get('speed_ratio', 1.0)
    volume_ratio = data.get('volume_ratio', 1.0)
    
    if session_id in active_connections:
        proxy = active_connections[session_id]
        
        async def init_task():
            success = await proxy.init_tts(voice_id, speed_ratio, volume_ratio)
            socketio.emit('tts_initialized', {
                'success': success,
                'session_id': session_id
            })
            
        asyncio.run_coroutine_threadsafe(init_task(), proxy.loop)

@socketio.on('send_audio')
def handle_send_audio(data):
    """处理音频发送"""
    session_id = request.sid
    audio_data = data.get('audio')
    transcript = data.get('transcript', '')
    
    if session_id in active_connections:
        proxy = active_connections[session_id]
        
        async def send_task():
            success = await proxy.send_audio(audio_data, transcript)
            socketio.emit('audio_sent', {
                'success': success,
                'session_id': session_id
            })
            
        asyncio.run_coroutine_threadsafe(send_task(), proxy.loop)

@socketio.on('create_realtime_response')
def handle_create_realtime_response(data):
    """创建实时对话响应"""
    session_id = request.sid
    role_id = data.get('role_id', 'default_role')
    conversation_history = data.get('conversation_history', [])
    
    if session_id in active_connections:
        proxy = active_connections[session_id]
        
        async def response_task():
            result = await proxy.create_realtime_response(role_id, conversation_history)
            socketio.emit('realtime_response_complete', {
                'result': result,
                'session_id': session_id
            })
            
        asyncio.run_coroutine_threadsafe(response_task(), proxy.loop)

@socketio.on('send_tts_text')
def handle_send_tts_text(data):
    """处理TTS文本发送"""
    session_id = request.sid
    text = data.get('text', '')
    
    if session_id in active_connections:
        proxy = active_connections[session_id]
        
        async def tts_task():
            result = await proxy.send_tts_text(text)
            socketio.emit('tts_complete', {
                'result': result,
                'session_id': session_id
            })
            
        asyncio.run_coroutine_threadsafe(tts_task(), proxy.loop)

if __name__ == '__main__':
    print("启动Vocal Auto Web Client...")
    print("访问地址: http://localhost:5000")
    socketio.run(app, host='0.0.0.0', port=5000, debug=True)
