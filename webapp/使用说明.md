# 🎯 Vocal Auto Web Client 使用说明

## ✅ 已完成的功能更新

根据您提供的参数要求，我已经成功更新了Web Client的前端界面，现在支持以下配置选项：

### 🔧 **实时对话配置**

#### 1. **环境配置**
- **Test环境** (默认) → 使用 `config/test.env.json`
- **UAT环境** → 使用 `config/uat.env.json`

#### 2. **角色ID**
- **银河精灵** (`yinhejingling`) - 默认选项
- **毒舌小宇** (`dushexiaoyu`)

#### 3. **模式**
- **Normal** (默认)
- **Child**

#### 4. **音量**
- 默认值：1.0
- 范围：0.1 - 2.0 倍
- 支持0.1步进调节

#### 5. **语速**
- 默认值：1.0
- 范围：0.5 - 2.0 倍
- 支持0.1步进调节

#### 6. **输出语言**
- **普通话** (默认)
- **粤语**

### 🎵 **TTS配置**

#### 1. **环境配置**
- **Test环境** (默认)
- **UAT环境**

#### 2. **音色ID**
- **银河精灵** (`yinhejingling`) - 默认选项
- **毒舌小宇** (`dushexiaoyu`)

#### 3. **情感**
- **默认** (无情感)
- **开心** (`happy`)
- **悲伤** (`sad`)
- **愤怒** (`angry`)
- **平静** (`calm`)

#### 4. **语言**
- **普通话** (默认)
- **粤语**

#### 5. **语速和音量**
- 与实时对话相同的配置选项

## 🚀 **使用流程**

### **实时对话模式**
1. 选择环境配置（Test/UAT）
2. 选择角色ID（银河精灵/毒舌小宇
3. 选择模式（Normal/Child）
4. 设置输出语言（普通话/粤语）
5. 调节音量和语速
6. 点击"初始化连接"
7. 开始语音对话或文本输入

### **TTS模式**
1. 选择环境配置（Test/UAT）
2. 选择音色ID（银河精灵/毒舌小宇
3. 选择情感和语言
4. 调节语速和音量
5. 点击"初始化"
6. 输入文本并生成语音

## 🔄 **重置功能**

每个模式都提供了"重置配置"按钮，可以一键恢复所有参数到默认值：

- **实时对话重置**：Test环境 + 银河精灵 + Normal模式 + 普通话 + 1.0倍音量语速
- **TTS重置**：Test环境 + 银河精灵 + 无情感 + 普通话 + 1.0倍音量语速

## 🎯 **技术实现**

### **前端更新**
- ✅ HTML界面：添加了下拉选择框和数值输入框
- ✅ JavaScript：支持参数收集和发送到后端
- ✅ 用户体验：添加了重置按钮和参数验证

### **后端更新**
- ✅ 环境配置：支持动态加载test.env.json和uat.env.json
- ✅ 参数传递：所有前端参数都正确传递到API
- ✅ 错误处理：增强了连接失败和参数错误的处理

### **配置文件支持**
- ✅ Test环境：`config/test.env.json`
- ✅ UAT环境：`config/uat.env.json`
- ✅ 动态切换：根据用户选择自动加载对应配置

## 📱 **界面预览**

现在的界面包含：
- 🔧 **配置区域**：两行布局，参数选择更直观
- 🎤 **对话区域**：支持语音录制和文本输入
- 🔊 **音频控制**：实时播放AI回复的语音
- 📊 **系统日志**：显示详细的操作和错误信息

## 🌐 **访问地址**

Web Client正在运行：**http://localhost:5000**

## 🎉 **完成状态**

✅ 所有您要求的参数选项都已实现  
✅ 前端界面已更新完成  
✅ 后端逻辑已适配新参数  
✅ 环境配置动态切换已实现  
✅ 用户体验优化完成  

现在您可以在浏览器中体验完整的参数化语音对话和TTS功能！🎯
