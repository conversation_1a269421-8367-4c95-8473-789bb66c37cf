import pandas as pd
import argparse
from tqdm import tqdm
import os
from openai import OpenAI
from concurrent.futures import ThreadPoolExecutor, as_completed

MODEL_NAME = "qwen2-72b-instruct"
SYSTEM_PROMPT = ""
MAX_WORKERS = 5  # 根据API限流调整并发数

def initialize_client(api_key):
    """初始化OpenAI客户端"""
    return OpenAI(
        api_key=api_key,
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    )

def process_conversation(client, user_input, history):
    """使用OpenAI兼容API处理单轮对话"""
    messages = []

    if SYSTEM_PROMPT:
        messages.append({"role": "system", "content": SYSTEM_PROMPT})
    
    # 添加历史对话
    for interaction in history:
        messages.append({"role": "user", "content": interaction["user"]})
        messages.append({"role": "assistant", "content": interaction["assistant"]})
    
    # 添加当前输入
    messages.append({"role": "user", "content": user_input})
    
    try:
        response = client.chat.completions.create(
            model=MODEL_NAME,
            messages=messages,
            temperature=0.7,
            top_p=0.9,
            max_tokens=1024
        )
        
        if response.choices:
            return response.choices[0].message.content.strip()
        return "[空响应]"
        
    except Exception as e:
        return f"[API错误] {str(e)}"

def process_single_case(client, case_id, case_data):
    """处理单个测试用例的所有对话轮次"""
    dialog_history = []
    case_results = []
    
    # 确保按对话轮次顺序处理
    for _, row in case_data.sort_values("turn_num").iterrows():
        response = process_conversation(
            client=client,
            user_input=row["transcript"],
            history=dialog_history
        )
        
        case_results.append({
            "ID": row["ID"],
            "turns": row["turns"],
            "transcript": row["transcript"],
            "Qwen_output_transcript": response
        })
        
        # 更新对话历史
        dialog_history.append({
            "user": row["transcript"],
            "assistant": response
        })
    
    return case_results

def main():
    # 参数解析
    parser = argparse.ArgumentParser(description="Qwen并发处理脚本")
    parser.add_argument("input_file", help="输入Excel文件路径")
    parser.add_argument("--api_key", 
                      help="DashScope API密钥（环境变量：DASHSCOPE_API_KEY）",
                      default=os.getenv("DASHSCOPE_API_KEY"))
    parser.add_argument("--workers", 
                      type=int,
                      default=MAX_WORKERS,
                      help=f"并发线程数，默认：{MAX_WORKERS}")
    args = parser.parse_args()

    # 初始化客户端
    client = initialize_client(args.api_key)

    # 读取并预处理数据
    df = pd.read_excel(args.input_file, sheet_name="realtime")
    df["turn_num"] = df["turns"].str.extract(r"(\d+)").astype(int)
    df_sorted = df.sort_values(["ID", "turn_num"])

    # 按ID分组准备并发处理
    case_groups = [(case_id, group) for case_id, group in df_sorted.groupby("ID")]

    # 使用线程池并发处理不同ID
    results = []
    with ThreadPoolExecutor(max_workers=args.workers) as executor:
        # 提交所有任务
        futures = {
            executor.submit(
                process_single_case, 
                client,
                case_id, 
                group.copy()
            ): case_id for case_id, group in case_groups
        }

        # 使用tqdm显示进度
        progress = tqdm(
            as_completed(futures),
            total=len(futures),
            desc="处理用例",
            unit="case"
        )

        # 收集结果
        for future in progress:
            try:
                case_results = future.result()
                results.extend(case_results)
            except Exception as e:
                tqdm.write(f"处理异常: {str(e)}")

    # 保存结果
    output_df = pd.DataFrame(results)
    output_file = args.input_file.replace(".xlsx", "_Qwen_report.xlsx")
    output_df.to_excel(output_file, sheet_name="realtime", index=False)
    print(f"\n处理完成！结果已保存至: {output_file}")

if __name__ == "__main__":
    main()
