# Vocal Auto Web Client 环境配置文件
# 复制此文件为 .env 并根据实际情况修改配置

# ===========================================
# 应用基础配置
# ===========================================
FLASK_ENV=production
FLASK_DEBUG=0
SECRET_KEY=your-secret-key-here

# ===========================================
# 服务端口配置
# ===========================================
WEB_PORT=5000
NGINX_HTTP_PORT=80
NGINX_HTTPS_PORT=443

# ===========================================
# API配置 - 测试环境
# ===========================================
# TTS API配置
TTS_BASE_URL=wss://realtime-test.mach-drive.com/openapitts/v1/realtime-tts
TTS_TOKEN=your-tts-token-here

# Realtime API配置
REALTIME_BASE_URL=wss://realtime-test.mach-drive.com/openapigamma/v1/realtime-server?model=step-1o-voice&audio_type={audio_format}
REALTIME_TOKEN=your-realtime-token-here

# Role API配置
ROLE_BASE_URL=https://realtime-test.mach-drive.com
ROLE_TOKEN=your-role-token-here

# AIGC配置
AIGC_BASE_URL=http://10.117.55.245:3000/v1
AIGC_API_KEY=your-aigc-api-key-here
AIGC_MODEL=doubao-1-5-pro-32k

# Session配置
SESSION_URL_BASE=https://test-openapi-realtime.c.stepfun-inc.net/realtimeagent/trace/page
TTS_URL_BASE=https://openapi-trace-debug.c.ibasemind.com/tts-replay

# ===========================================
# 性能配置
# ===========================================
CONCURRENCY_LIMIT=1
MAX_HISTORY_TURNS=5

# ===========================================
# 日志配置
# ===========================================
LOG_LEVEL=INFO
LOG_FILE=/app/logs/vocal-auto.log

# ===========================================
# 数据库配置（如果需要）
# ===========================================
# DATABASE_URL=postgresql://user:password@localhost:5432/vocal_auto
# REDIS_URL=redis://localhost:6379/0

# ===========================================
# 监控配置
# ===========================================
# SENTRY_DSN=your-sentry-dsn-here
# PROMETHEUS_PORT=9090

# ===========================================
# SSL配置（HTTPS）
# ===========================================
# SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
# SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# ===========================================
# 备份配置
# ===========================================
BACKUP_RETENTION_DAYS=30
BACKUP_SCHEDULE="0 2 * * *"  # 每天凌晨2点备份

# ===========================================
# 安全配置
# ===========================================
# ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com
# CORS_ORIGINS=http://localhost:3000,https://your-frontend.com
