#!/bin/bash

# Vocal Auto Web Client 云端部署脚本
# 使用方法: ./deploy.sh [环境] [操作]
# 环境: dev, test, prod
# 操作: build, start, stop, restart, logs, status

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 默认参数
ENVIRONMENT=${1:-"test"}
ACTION=${2:-"start"}
PROJECT_NAME="vocal-auto-web"
COMPOSE_FILE="docker-compose.yml"

# 检查Docker和Docker Compose
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 构建镜像
build_image() {
    log_info "构建Docker镜像..."
    docker-compose -f $COMPOSE_FILE build --no-cache
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    docker-compose -f $COMPOSE_FILE up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if docker-compose -f $COMPOSE_FILE ps | grep -q "Up"; then
        log_success "服务启动成功"
        show_status
    else
        log_error "服务启动失败"
        docker-compose -f $COMPOSE_FILE logs
        exit 1
    fi
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    docker-compose -f $COMPOSE_FILE down
    log_success "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    stop_services
    start_services
}

# 显示日志
show_logs() {
    log_info "显示服务日志..."
    docker-compose -f $COMPOSE_FILE logs -f --tail=100
}

# 显示状态
show_status() {
    log_info "服务状态:"
    docker-compose -f $COMPOSE_FILE ps
    
    echo ""
    log_info "访问地址:"
    echo "  - Web界面: http://localhost:5000"
    echo "  - Nginx代理: http://localhost:80"
    
    echo ""
    log_info "健康检查:"
    if curl -f http://localhost:5000/ &> /dev/null; then
        log_success "Web服务正常"
    else
        log_warning "Web服务可能未就绪"
    fi
}

# 清理资源
cleanup() {
    log_info "清理Docker资源..."
    docker-compose -f $COMPOSE_FILE down -v --remove-orphans
    docker system prune -f
    log_success "清理完成"
}

# 备份数据
backup_data() {
    BACKUP_DIR="backup/$(date +%Y%m%d_%H%M%S)"
    log_info "备份数据到 $BACKUP_DIR..."
    
    mkdir -p $BACKUP_DIR
    
    # 备份配置文件
    cp -r config $BACKUP_DIR/
    
    # 备份数据目录
    if [ -d "data" ]; then
        cp -r data $BACKUP_DIR/
    fi
    
    # 备份报告目录
    if [ -d "reports" ]; then
        cp -r reports $BACKUP_DIR/
    fi
    
    log_success "数据备份完成: $BACKUP_DIR"
}

# 主函数
main() {
    echo "=== Vocal Auto Web Client 部署脚本 ==="
    echo "环境: $ENVIRONMENT"
    echo "操作: $ACTION"
    echo ""
    
    check_dependencies
    
    case $ACTION in
        "build")
            build_image
            ;;
        "start")
            start_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "logs")
            show_logs
            ;;
        "status")
            show_status
            ;;
        "cleanup")
            cleanup
            ;;
        "backup")
            backup_data
            ;;
        "deploy")
            build_image
            start_services
            ;;
        *)
            log_error "未知操作: $ACTION"
            echo "支持的操作: build, start, stop, restart, logs, status, cleanup, backup, deploy"
            exit 1
            ;;
    esac
}

# 执行主函数
main
