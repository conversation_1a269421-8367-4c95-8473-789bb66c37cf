import websockets
import json
import ssl


class WebSocketClient:
    def __init__(self, uri, headers=None):
        self.uri = uri
        # Ensure Content-Type with UTF-8 is set
        # updated_headers = (headers or {}).copy() # Handle None headers and work on a copy
        # updated_headers['Content-Type'] = 'application/json; charset=UTF-8'
        self.headers = headers
        self.websocket = None
        self.ssl_context = ssl.create_default_context()
        self.ssl_context.check_hostname = False
        self.ssl_context.verify_mode = ssl.CERT_NONE

    async def connect(self, headers=None):
        updated_headers = (headers or self.headers).copy()
        updated_headers['Content-Type'] = 'application/json; charset=UTF-8'
        self.websocket = await websockets.connect(
            self.uri,
            ssl=self.ssl_context,
            additional_headers=updated_headers,
            ping_interval=None,
            ping_timeout=None,
            max_size=None
        )
        print(f"Connected to {self.uri}")
        print(f"Headers: {updated_headers}")

    async def send(self, message):
        await self.websocket.send(json.dumps(message, ensure_ascii=False))
        # print(f"Sent message: {message}")

    async def receive(self):
        response = await self.websocket.recv()
        if response is None:
            raise ConnectionError("WebSocket连接已关闭，接收到的响应为空")
        return json.loads(response)

    async def close(self):
        await self.websocket.close()
        print("WebSocket connection closed")