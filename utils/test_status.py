#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from enum import Enum, auto

class TestStatus(Enum):
    """测试状态枚举类"""
    SUCCESS = auto()  # 成功
    FAILED = auto()   # 失败
    SKIPPED = auto()  # 跳过
    PENDING = auto()  # 等待中

    def __str__(self):
        """返回状态的字符串表示"""
        if self == TestStatus.SUCCESS:
            return "Success"
        elif self == TestStatus.FAILED:
            return "Failed"
        elif self == TestStatus.SKIPPED:
            return "Skipped"
        elif self == TestStatus.PENDING:
            return "Pending"
        else:
            return "Unknown"
    
    @staticmethod
    def from_string(status_str):
        """从字符串转换为状态枚举"""
        if not status_str:
            return None
            
        status_str = status_str.lower()
        if status_str in ["success", "passed"]:
            return TestStatus.SUCCESS
        elif status_str == "failed":
            return TestStatus.FAILED
        elif status_str == "skipped":
            return TestStatus.SKIPPED
        elif status_str == "pending":
            return TestStatus.PENDING
        else:
            return None 