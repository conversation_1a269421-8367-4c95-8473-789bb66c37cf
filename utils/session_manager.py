#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import uuid
from typing import Dict, Optional, Any
from .config_manager import get_config_manager

class SessionManager:
    """会话管理器，处理会话ID和会话URL的创建和管理"""
    
    @staticmethod
    def create_session_id(username: Optional[str] = None) -> str:
        """
        创建会话ID
        
        Args:
            username: 用户名，用于生成带有用户标识的会话ID
            
        Returns:
            str: 生成的会话ID
        """
        session_id = f"mach_test_{uuid.uuid4().hex}"
        if username:
            session_id = f"{session_id}_{username}"
        return session_id
    
    @staticmethod
    def create_session_url(session_id: str) -> str:
        """
        根据会话ID创建会话URL
        
        Args:
            session_id: 会话ID
            
        Returns:
            str: 会话URL
        """
        config = get_config_manager()
        url_base = config.get("session.url_base")
        return f"{url_base}?session_id={session_id}"

    @staticmethod
    def create_tts_session_url(session_id: str) -> str:
        """
        根据会话ID创建会话URL
        
        Args:
            session_id: 会话ID
            
        Returns:
            str: 会话URL
        """
        config = get_config_manager()
        url_base = config.get("session.tts_url_base")
        return f"{url_base}?trace_id={session_id}"
