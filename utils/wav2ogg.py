from pydub import AudioSegment
import os

class AudioConverter:
    def __init__(self):
        pass

    def wav_to_ogg_speex(self, input_wav_file, output_ogg_file):
        try:
            audio = AudioSegment.from_wav(input_wav_file)
            audio = audio.set_frame_rate(16000)  # 设置采样率为 16000
            audio = audio.set_channels(1)  # 设置为单通道
            audio = audio.set_sample_width(2)  # 设置为16位
            audio.export(output_ogg_file, format="ogg", codec="libspeex")
            print(f"转换成功，输出文件: {output_ogg_file}")
        except Exception as e:
            print(f"转换时出现错误: {e}")

    def convert_directory(self, input_dir, output_dir, file_ext='.wav'):
        # 检查输入目录和输出目录是否存在，不存在则创建输出目录
        if not os.path.isdir(input_dir):
            print(f"输入目录 {input_dir} 不存在，请检查路径。")
        else:
            if not os.path.isdir(output_dir):
                os.makedirs(output_dir)

            # 遍历输入目录下所有指定扩展名的文件
            for root, dirs, files in os.walk(input_dir):
                for file in files:
                    if file.lower().endswith(file_ext):
                        input_file_path = os.path.join(root, file)
                        file_name_without_ext = os.path.splitext(file)[0]
                        output_file_path = os.path.join(output_dir, file_name_without_ext + '.ogg')
                        if file_ext == '.wav':
                            self.wav_to_ogg_speex(input_file_path, output_file_path)


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='将WAV文件转换为OGG格式')
    parser.add_argument('input', help='输入WAV文件目录')
    parser.add_argument('output', help='输出OGG文件目录')
    
    args = parser.parse_args()
    
    converter = AudioConverter()
    converter.convert_directory(args.input, args.output)

