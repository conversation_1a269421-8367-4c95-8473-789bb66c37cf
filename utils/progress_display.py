#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from typing import Dict, List, Any
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn, TimeRemainingColumn, TimeElapsedColumn
from utils.test_status import TestStatus

class ProgressDisplay:
    """进度显示类"""

    def __init__(self):
        """初始化进度显示类"""
        self.console = Console()
        self.progress = Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            TextColumn("剩余时间:"),
            TimeRemainingColumn(),
            TextColumn("已用时间:"),
            TimeElapsedColumn(),
            console=self.console,
            refresh_per_second=10  # 增加刷新频率
        )
        self.current_progress = 0  # 添加当前进度追踪

    def start_progress(self, total: int, description: str = "处理中..."):
        """开始显示进度

        Args:
            total: 总任务数
            description: 进度描述
        """
        self.total = total  # 保存总任务数
        self.current_progress = 0  # 初始化当前进度
        self.task_id = self.progress.add_task(description, total=total)
        self.progress.start()

    def update_progress(self, advance: int = 1, complete: bool = False):
        """更新进度

        Args:
            advance: 进度增量
            complete: 是否直接完成进度
        """
        if complete:
            self.current_progress = self.total
            self.progress.update(self.task_id, completed=self.total)
        else:
            self.current_progress = min(self.current_progress + advance, self.total)
            self.progress.update(self.task_id, completed=self.current_progress)

    def stop_progress(self):
        """停止显示进度"""
        if self.current_progress < self.total:
            self.update_progress(complete=True)
        self.progress.stop()

    def display_results(self, results: List[Dict[str, Any]]):
        """显示测试结果

        Args:
            results: 测试结果列表
        """
        # 按ID分组统计
        grouped_results = {}
        for result in results:
            # 获取ID，确保其为字符串类型
            id_value = result.get('ID', '')
            id_str = str(id_value) if id_value is not None else ""
            
            # 如果这个ID还没有在字典中，添加它
            if id_str not in grouped_results:
                grouped_results[id_str] = []
            
            # 将结果添加到对应ID的列表中
            grouped_results[id_str].append(result)
            
        # 显示每个对话轮次的详细信息
        detailed_table = Table(title="测试结果")
        detailed_table.add_column("ID", style="cyan")
        detailed_table.add_column("轮次", style="blue", justify="center")
        detailed_table.add_column("功能模块", style="magenta")
        detailed_table.add_column("测试用例", style="green")
        detailed_table.add_column("状态", style="red")
        detailed_table.add_column("错误信息", style="yellow")
        
        # 添加每个轮次的详细信息
        for id_str, id_results in grouped_results.items():
            for i, result in enumerate(id_results):
                # 确保所有值都被转换为字符串
                status = result.get('status')
                status_str = str(status) if status is not None else ""
                
                # 安全获取其他值并转换为字符串
                module_value = result.get('功能模块', '')
                module_str = str(module_value) if module_value is not None else ""
                
                case_value = result.get('测试用例', '')
                case_str = str(case_value) if case_value is not None else ""
                
                error_value = result.get('error', '')
                error_str = str(error_value) if error_value is not None else ""
                
                detailed_table.add_row(
                    id_str,
                    str(i + 1),
                    module_str,
                    case_str,
                    status_str,
                    error_str
                )
        
        self.console.print(detailed_table)

    def display_summary(self, results: List[Dict[str, Any]]):
        """显示测试摘要

        Args:
            results: 测试结果列表
        """
        # 按ID分组统计结果
        grouped_results = {}
        for result in results:
            # 获取ID，确保其为字符串类型
            id_value = result.get('ID', '')
            id_str = str(id_value) if id_value is not None else ""
            
            # 如果这个ID还没有在字典中，添加它
            if id_str not in grouped_results:
                grouped_results[id_str] = []
            
            # 将结果添加到对应ID的列表中
            grouped_results[id_str].append(result)
        
        # 计算总用例数（每个唯一ID视为一个用例）
        total = len(grouped_results)
        
        # 计算成功、跳过和失败的用例数
        success = 0
        skipped = 0
        failed = 0
        
        for id_str, results_list in grouped_results.items():
            # 检查这个ID的所有结果
            # 如果任何一个结果是失败的，整个用例视为失败
            # 如果所有结果都是跳过的，整个用例视为跳过
            # 否则，整个用例视为成功
            if any(str(r.get('status')) == str(TestStatus.FAILED) for r in results_list):
                failed += 1
            elif all(str(r.get('status')) == str(TestStatus.SKIPPED) for r in results_list):
                skipped += 1
            else:
                success += 1

        self.console.print(f"\n[bold]测试摘要:[/bold]")
        self.console.print(f"总测试用例数: {total}")
        self.console.print(f"成功: {success}")
        self.console.print(f"跳过: {skipped}")
        self.console.print(f"失败: {failed}")
        if total > 0:
            self.console.print(f"成功率: {(success/total)*100:.2f}%")
        else:
            self.console.print(f"成功率: 0.00% (没有执行任何测试用例)")
