import requests
import json


def send_feishu_bot_message(data: dict):
    """
    通过飞书机器人 Webhook 发送带@人的自动化执行报告消息

    :param data: 消息内容数据，格式如上面的json说明
    """
    webhook_url = "https://open.feishu.cn/open-apis/bot/v2/hook/55e03093-2935-4d6b-bac7-004735de1733"
    user_map = {
        "彭斯亮": "7470322272130072579",
        "贾晓亮": "7470322287665807363",
        "童强":"7490474529421344787",
        "袁煜佳": "7493795847956676612",
        "朱荣青": "7487846424178294803",
        "熊力宏": "7470322287665807363",
        "张宇晶": "7478514217600303107",
        "崔晋梅": "7472728520108670980"
    }

    user_name = data["user_name"]
    if user_name in user_map:
        at_user_id = user_map[user_name]
    else:
        at_user_id = None
    total = data["total"]
    success = data["success"]
    fail = data["fail"]
    tasks = data["tasks"]
    skip = data["skip"]
    # 构造内容块
    content_blocks = [
        [
            {"tag": "at", "user_id": at_user_id},
            {"tag": "text",
             "text": f" jenkins自动化任务已执行完成，本轮测试\n总运行 {total} 条 成功 {success} 条 失败 {fail} 条 跳过 {skip} 条，请查看测试报告："}
        ]
    ]

    for task in tasks:
        name = task["name"]
        report = task["report_link"]
        audio = task["audio_link"]
        content_blocks.append([
            {"tag": "text", "text": f"\n\n【{name}】: "},
            {"tag": "a", "text": "测试报告链接", "href": report}
        ])
        content_blocks.append([
            {"tag": "text", "text": f"测试生成音频: "},
            {"tag": "a", "text": "音频链接", "href": audio}
        ])

    message_data = {
        "msg_type": "post",
        "content": {
            "post": {
                "zh_cn": {
                    "title": "自动化测试结果通知",
                    "content": content_blocks
                }
            }
        }
    }

    headers = {
        "Content-Type": "application/json"
    }

    response = requests.post(webhook_url, headers=headers, data=json.dumps(message_data, ensure_ascii=False))

    if response.status_code == 200:
        print("消息发送成功！")
    else:
        print(f"消息发送失败，状态码: {response.status_code}, 返回信息: {response.text}")


if __name__ == "__main__":
      # 举例
    report_data = {
        "user_name": "童强",  # 替换为需要@的用户 ID
        "total": 100,  # 总共case数
        "skip":5,  # 跳过case数
        "success": 80, # 成功case数
        "fail": 15, # 失败case数
        "tasks": [
            {
                "name": "儿童模式", # 运行用例名称
                "report_link": "http://*************:8080/1/%E7%AB%AF%E5%88%B0%E7%AB%AF%E8%AF%AD%E9%9F%B3/bmk%E6%B5%8B%E8%AF%95%E6%8A%A5%E5%91%8A", # 测试报告链接
                "audio_link": "http://*************:8080/1/%E7%AB%AF%E5%88%B0%E7%AB%AF%E8%AF%AD%E9%9F%B3/bmk%E6%B5%8B%E8%AF%95%E6%8A%A5%E5%91%8A"  # 测试生成音频链接
            },
            {
                "name": "唤醒词",
                "report_link": "http://*************:8080/1/%E7%AB%AF%E5%88%B0%E7%AB%AF%E8%AF%AD%E9%9F%B3/bmk%E6%B5%8B%E8%AF%95%E6%8A%A5%E5%91%8A",
                "audio_link": "http://*************:8080/1/%E7%AB%AF%E5%88%B0%E7%AB%AF%E8%AF%AD%E9%9F%B3/bmk%E6%B5%8B%E8%AF%95%E6%8A%A5%E5%91%8A"
            }
        ]   # tasks 里参数可以自由增加
    }
    send_feishu_bot_message(report_data)