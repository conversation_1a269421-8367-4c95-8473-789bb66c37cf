import os
import pandas as pd
from typing import Any

class ExcelWriter:

    @staticmethod
    def write_to_excel(excel_path, sheet_name, data_dict):
        """
        写入数据到Excel文件
        Args:
            excel_path: Excel文件路径
            sheet_name: 工作表名称
            data_dict: 要写入的数据，格式为 {row_index: {column_name: value}}
        """
        if not os.path.exists(excel_path):
            raise FileNotFoundError(f"Excel文件不存在: {excel_path}")
        
        df = pd.read_excel(excel_path, sheet_name=sheet_name)
        
        for row_index, column_updates in data_dict.items():
            for column_name, value in column_updates.items():
                df.at[row_index, column_name] = value
        
        with pd.ExcelWriter(excel_path, engine='openpyxl', mode='a', if_sheet_exists='overlay') as writer:
            df.to_excel(writer, sheet_name=sheet_name, index=False)
    
