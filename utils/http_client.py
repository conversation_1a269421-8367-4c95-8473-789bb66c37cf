import requests
from requests.adapters import <PERSON><PERSON><PERSON><PERSON>pter
from urllib3.util.retry import Retry
import logging
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

class HTTPClient:
    def __init__(self, base_url: str, headers: dict, retries: int = 3):
        self.base_url = base_url
        # Ensure Content-Type with UTF-8 is set
        updated_headers = headers.copy() # Work on a copy
        # Add/overwrite Content-Type, common practice for clients sending JSON
        updated_headers['Content-Type'] = 'application/json; charset=UTF-8' 
        self.headers = updated_headers
        self.session = requests.Session()
        print("[DEBUG] HttpClient headers:")
        print(self.headers)
        retry = Retry(
            total=retries,
            backoff_factor=0.3,
            status_forcelist=(500, 502, 504),
            allowed_methods=frozenset(['GET', 'POST', 'PUT', 'DELETE'])
        )
        self.session.mount('http://', HTTPAdapter(max_retries=retry))
        self.session.mount('https://', HTTPAdapter(max_retries=retry))

    def _handle_response(self, response: requests.Response) -> Dict:
        try:
            response.raise_for_status()
            return response.json()
        except requests.exceptions.HTTPError as e:
            logger.error(f"HTTP error: {str(e)}")
            return {
                "error": str(e),
                "status_code": response.status_code,
                "content": response.text
            }
        except ValueError:
            return {"content": response.text}

    def get(self, endpoint: str, params: Optional[Dict] = None) -> Dict:
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        return self._handle_response(self.session.get(url, params=params, headers=self.headers))

    def post(self, endpoint: str, data: Dict[str, Any]) -> Dict:
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        return self._handle_response(self.session.post(url, json=data, headers=self.headers))

    def put(self, endpoint: str, data: Dict[str, Any]) -> Dict:
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        return self._handle_response(self.session.put(url, json=data, headers=self.headers))

    def delete(self, endpoint: str) -> Dict:
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        return self._handle_response(self.session.delete(url, headers=self.headers))