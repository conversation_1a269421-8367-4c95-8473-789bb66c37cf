import re

def count_text_chars(text):
    # 去除空格和换行
    text = re.sub(r'[\s]+', '', text)

    # 中文字符（包括汉字）
    chinese_chars = re.findall(r'[\u4e00-\u9fff]', text)

    # 英文单词
    english_words = re.findall(r'[a-zA-Z]+', text)

    # 去除英文单词和中文后的剩余符号
    text_cleaned = re.sub(r'[\u4e00-\u9fff]|[a-zA-Z]+', '', text)

    # 剩下的是符号、数字等
    symbols = list(text_cleaned)

    total_count = len(chinese_chars) + len(english_words) + len(symbols)

    return total_count