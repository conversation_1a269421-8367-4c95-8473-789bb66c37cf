import pandas as pd
import math

def is_empty_or_nan(value) -> bool:
    """
    Check if the value is None, NaN (pandas or standard), empty string, 
    or a string containing only whitespace.

    Args:
        value: The value to check.

    Returns:
        bool: True if the value is empty or NaN, False otherwise.
    """
    if value is None:
        return True
    # Check for empty or whitespace-only string
    if isinstance(value, str) and not value.strip():
        return True
    # Check for pandas NaN using pd.isna which handles various types
    if pd.isna(value):
        return True
    # Check for standard float NaN (pd.isna might cover this, but being explicit)
    if isinstance(value, float) and math.isnan(value):
        return True
    return False

def clean_string(value) -> str:
    """
    Convert None or NaN to an empty string "", and strip whitespace from strings.
    If the input is of another type, try converting it to a string.

    Args:
        value: The value to clean.

    Returns:
        str: The cleaned string.
    """
    if is_empty_or_nan(value):
        return ""
    
    return str(value).strip() # Ensure output is string and stripped

def get_cleaned_value_from_row(row_data, column_name, default_value_if_nan_or_missing):
    """Helper function to get a value from a Pandas Series (row), handle NaN/missing, and clean it."""
    val = row_data.get(column_name) # Get value, None if column doesn't exist
    if pd.isna(val): # Check if value is NaN (this also handles if val is already None from .get())
        val = default_value_if_nan_or_missing
    return clean_string(val)