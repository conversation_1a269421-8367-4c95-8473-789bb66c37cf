#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
from pathlib import Path

_config_manager_instance = None

def get_config_manager(config_file=None, force_reload=False):
    global _config_manager_instance
    if _config_manager_instance is None or force_reload:
        _config_manager_instance = ConfigManager(config_file)
    return _config_manager_instance

class ConfigManager:
    """配置管理工具，用于管理API密钥、URL等配置信息"""

    def __init__(self, config_file=None):
        """初始化配置管理工具

        Args:
            config_file: 配置文件路径，默认为None，表示使用默认配置
        """
        self.config = {
            # TTS API配置
            "tts": {
                "base_url": "wss://mcgpt-test.mach-drive.com/openapitts/v1/realtime-tts",
                "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIwMDEiLCJuYW1lIjoiYWRtaW4ifQ.SO7x6bvEBtOoAJPo1mXuFuYIm14T6GkuFOOowU5GJrk"
            },
            # 实时对话API配置
            "realtime": {
                "base_url": "wss://mcgpt-test.mach-drive.com/openapigamma/v1/realtime-server?model=step-1o-voice&audio_type={audio_format}",
                "token": "MjFjYTZjNGRmNjQzNDM1ZjgwOWU3OTIzYWIyMTUyNjU="
            },
            # 角色API配置
            "role": {
                "base_url": "https://mcgpt-test.mach-drive.com",
                "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIwMDEiLCJuYW1lIjoiYWRtaW4ifQ.SO7x6bvEBtOoAJPo1mXuFuYIm14T6GkuFOOowU5GJrk"
            },
            # 生成式闲聊API配置
            "aigc_mode": {
                "base_url": "http://10.117.55.245:3000/v1",
                "api_key": "sk-wUnjOsVrS773MLLdBaC3B5177eD74736BbC6C48906D2100b",
                "model": "doubao-1-5-pro-32k",
                "prompt_config": "config/prompt_config.yaml",
            },
            # 会话URL配置
            "session": {
                "url_base": "https://openapi-resource.stepfun-inc.com/realtimeagent/trace/page",
                "tts_url_base": "https://openapi-trace-debug.c.ibasemind.com/json"
            },
            # 其他配置
            "concurrency_limit": 1,
            "max_history_turns": 5
        }

        # 如果提供了配置文件，则加载配置
        if config_file and os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    # 更新配置，但不覆盖默认配置中不存在的键
                    self._update_config(self.config, user_config)
            except Exception as e:
                print(f"加载配置文件失败: {str(e)}")

        # 从环境变量加载配置
        self._load_from_env()

        # 初始化后自动打印完整配置，便于debug
        self.print_config()

    def _update_config(self, config, updates):
        """递归更新配置

        Args:
            config: 要更新的配置
            updates: 更新的内容
        """
        for key, value in updates.items():
            if key in config and isinstance(config[key], dict) and isinstance(value, dict):
                self._update_config(config[key], value)
            else:
                config[key] = value

    def _load_from_env(self):
        """从环境变量加载配置"""
        # TTS配置
        if os.environ.get("TTS_TOKEN"):
            self.config["tts"]["token"] = os.environ.get("TTS_TOKEN")

        # 实时对话配置
        if os.environ.get("REALTIME_TOKEN"):
            self.config["realtime"]["token"] = os.environ.get("REALTIME_TOKEN")

        # 角色API配置
        if os.environ.get("ROLE_TOKEN"):
            self.config["role"]["token"] = os.environ.get("ROLE_TOKEN")

        # 生成式闲聊API配置
        if os.environ.get("AIGC_BASE_URL"):
            self.config["aigc_mode"]["base_url"] = os.environ.get("AIGC_BASE_URL")
        if os.environ.get("AIGC_API_KEY"):
            self.config["aigc_mode"]["api_key"] = os.environ.get("AIGC_API_KEY")
        if os.environ.get("AIGC_MODEL"):
            self.config["aigc_mode"]["model"] = os.environ.get("AIGC_MODEL")

        # 其他配置
        if os.environ.get("CONCURRENCY_LIMIT"):
            self.config["concurrency_limit"] = int(os.environ.get("CONCURRENCY_LIMIT"))
        if os.environ.get("MAX_HISTORY_TURNS"):
            self.config["max_history_turns"] = int(os.environ.get("MAX_HISTORY_TURNS"))

    def get(self, key, default=None):
        """获取配置值

        Args:
            key: 配置键，支持点号分隔的多级键，如"tts.base_url"
            default: 默认值，如果配置不存在则返回此值

        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        return value

    def print_config(self):
        """打印当前完整配置，便于debug"""
        import json
        print(json.dumps(self.config, indent=4, ensure_ascii=False))

